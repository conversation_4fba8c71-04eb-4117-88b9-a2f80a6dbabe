# AI小熊聊天客户端测试说明

## 🔧 **CORS问题解决**

### 问题原因
浏览器发送 `OPTIONS` 请求是因为：
1. **跨域请求** - HTML文件和API服务器不在同一域名/端口
2. **预检请求** - 使用了 `application/json` Content-Type 的 POST 请求

### 解决方案
已在 `main.py` 中添加了CORS中间件：

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)
```

## 🚀 **测试步骤**

### 1. 启动后端服务
```bash
python main.py
```
服务器将在 `http://localhost:8712` 启动

### 2. 打开HTML客户端
在浏览器中打开 `static/ai_bear_chat.html`

### 3. 测试功能

#### 文本聊天测试
1. 在输入框中输入文字
2. 点击发送按钮或按回车键
3. 观察AI小熊的回复

#### 语音聊天测试
1. 点击麦克风按钮开始录音
2. 说话后再次点击停止录音
3. 系统会自动进行语音识别并回复

## 🔍 **调试信息**

### 网络请求流程
1. **OPTIONS请求** - 浏览器预检请求（正常现象）
2. **POST请求** - 实际的API调用

### 查看请求详情
打开浏览器开发者工具（F12）：
- **Network标签** - 查看HTTP请求
- **Console标签** - 查看JavaScript日志

### 常见问题

#### 1. CORS错误
```
Access to fetch at 'http://localhost:8712/talk' from origin 'null' has been blocked by CORS policy
```
**解决方案**: 确保后端已添加CORS中间件

#### 2. 连接拒绝
```
Failed to fetch
```
**解决方案**: 确保后端服务正在运行

#### 3. 麦克风权限
```
无法访问麦克风，请检查权限设置
```
**解决方案**: 在浏览器中允许麦克风权限

## 📝 **API接口说明**

### POST /talk
**请求格式**:
```json
// 文本输入
{
    "text": "你好"
}

// 语音输入
{
    "audio": "base64编码的音频数据"
}
```

**响应格式**:
```json
{
    "resp": "AI回复的文本"
}
```

## 🎯 **测试建议**

1. **先测试文本** - 确保基本功能正常
2. **再测试语音** - 需要麦克风权限
3. **观察状态** - 小熊的状态指示灯会变化
4. **查看动画** - AI回复时小熊会有动画效果

## 🔧 **自定义配置**

### 修改API地址
在 `ai_bear_chat.html` 中修改：
```javascript
const API_BASE_URL = 'http://your-server:port';
```

### 修改样式
可以在CSS部分自定义：
- 背景颜色
- 小熊样式
- 聊天气泡样式
- 动画效果

## 📱 **移动端支持**

HTML客户端支持响应式设计，可以在手机浏览器中使用：
- 自动适配屏幕尺寸
- 触摸友好的按钮
- 移动端语音录制支持

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket AI小熊聊天</title>
</head>
<body>
    <h1>WebSocket AI小熊聊天</h1>

    <!-- WebSocket连接区域 -->
    <div>
        <h2>WebSocket连接</h2>
        <input type="text" id="wsUrl" value="ws://127.0.0.1:8712/main" style="width: 300px;">
        <button id="connectBtn" onclick="toggleConnection()">连接</button>
        <span id="connectionStatus">未连接</span>
    </div>

    <!-- 压力传感器按钮 -->
    <div>
        <h2>压力传感器</h2>
        <button id="faceBtn" onmousedown="startPressure('face', this)" onmouseup="endPressure('face', this)" onmouseleave="endPressure('face', this)">
            Face按钮 <span id="facePressure" style="display:none;">1</span>
        </button>
        <button id="neckBtn" onmousedown="startPressure('neck', this)" onmouseup="endPressure('neck', this)" onmouseleave="endPressure('neck', this)">
            Neck按钮 <span id="neckPressure" style="display:none;">1</span>
        </button>
    </div>

    <!-- 语音交互 -->
    <div>
        <h2>语音交互</h2>
        <button id="voiceBtn" onmousedown="startVoice()" onmouseup="stopVoice()" onmouseleave="stopVoice()">
            按住说话
        </button>
        <span id="voiceStatus">未录音</span>
    </div>

    <!-- 音频播放区域 -->
    <div>
        <h2>音频播放</h2>
        <audio id="audioPlayer" controls></audio>
        <div id="audioStatus">等待音频...</div>
    </div>

    <!-- 日志区域 -->
    <div>
        <h2>日志</h2>
        <textarea id="logArea" rows="10" cols="80" readonly></textarea>
        <br>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <!-- 引入音频处理模块 -->
    <script src="js/audio-processor.js"></script>

    <script>
        // 全局变量
        let websocket = null;
        let audioProcessor = null;
        let isConnected = false;
        let isRecording = false;

        // 压力传感器相关
        let pressureTimers = {};
        let pressureValues = {};
        let pressingParts = new Set();

        // 音频播放相关
        let audioQueue = [];
        let isPlaying = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            audioProcessor = new AudioProcessor();
            log('页面加载完成');
        });

        // WebSocket连接管理
        function toggleConnection() {
            if (isConnected) {
                disconnect();
            } else {
                connect();
            }
        }

        function connect() {
            const wsUrl = document.getElementById('wsUrl').value;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    isConnected = true;
                    updateConnectionStatus('已连接', 'green');
                    document.getElementById('connectBtn').textContent = '断开';
                    log('WebSocket连接成功');
                };

                websocket.onmessage = function(event) {
                    if (event.data instanceof Blob) {
                        // 接收到音频数据
                        handleAudioData(event.data);
                    } else {
                        // 接收到文本消息
                        log('收到消息: ' + event.data);
                    }
                };

                websocket.onclose = function(event) {
                    isConnected = false;
                    updateConnectionStatus('已断开', 'red');
                    document.getElementById('connectBtn').textContent = '连接';
                    log('WebSocket连接关闭');
                };

                websocket.onerror = function(error) {
                    log('WebSocket错误: ' + error);
                    updateConnectionStatus('连接错误', 'red');
                };

            } catch (error) {
                log('连接失败: ' + error.message);
                updateConnectionStatus('连接失败', 'red');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
            }
        }

        function updateConnectionStatus(status, color) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = status;
            statusElement.style.color = color;
        }

        // 压力传感器功能
        function startPressure(part, button) {
            if (pressingParts.has(part) || !isConnected) return;

            pressingParts.add(part);
            pressureValues[part] = 1;

            // 显示压力值
            const pressureSpan = document.getElementById(part + 'Pressure');
            pressureSpan.style.display = 'inline';
            pressureSpan.textContent = pressureValues[part];

            // 按钮样式变化
            button.style.backgroundColor = '#ccc';

            // 启动计时器
            const startTime = Date.now();
            pressureTimers[part] = setInterval(() => {
                const elapsed = (Date.now() - startTime) / 1000;

                if (elapsed >= 5) {
                    pressureValues[part] = 3;
                } else if (elapsed >= 2) {
                    pressureValues[part] = 2;
                } else {
                    pressureValues[part] = 1;
                }

                pressureSpan.textContent = pressureValues[part];
            }, 100);

            log(`开始按压 ${part}`);
        }

        function endPressure(part, button) {
            if (!pressingParts.has(part)) return;

            // 清理计时器
            if (pressureTimers[part]) {
                clearInterval(pressureTimers[part]);
                delete pressureTimers[part];
            }

            // 隐藏压力值
            const pressureSpan = document.getElementById(part + 'Pressure');
            pressureSpan.style.display = 'none';

            // 恢复按钮样式
            button.style.backgroundColor = '';

            // 发送压力数据
            const pressure = pressureValues[part] || 1;
            sendPressureData(part, pressure);

            // 清理状态
            pressingParts.delete(part);
            delete pressureValues[part];

            log(`结束按压 ${part}, 压力值: ${pressure}`);
        }

        function sendPressureData(part, pressure) {
            if (!isConnected || !websocket) return;

            const message = `PressCmd ${part}||${pressure}`;
            websocket.send(message);
            log(`发送压力数据: ${message}`);
        }

        // 语音交互功能
        async function startVoice() {
            if (isRecording || !isConnected) return;

            try {
                // 发送开始指令
                websocket.send('ClientStartCmd null');
                log('发送开始录音指令');

                // 开始流式录音
                await audioProcessor.startStreamingToWebSocket(websocket, 200);

                isRecording = true;
                document.getElementById('voiceStatus').textContent = '录音中...';
                document.getElementById('voiceBtn').style.backgroundColor = '#ff4444';

                log('开始语音录音');

            } catch (error) {
                log('开始录音失败: ' + error.message);
            }
        }

        async function stopVoice() {
            if (!isRecording) return;

            try {
                isRecording = false;
                document.getElementById('voiceStatus').textContent = '未录音';
                document.getElementById('voiceBtn').style.backgroundColor = '';

                log('停止语音录音');

                // 停止流式录音
                await audioProcessor.stopStreamingToWebSocket();

                // 发送停止指令
                if (isConnected && websocket) {
                    websocket.send('ClientStopCmd null');
                    log('发送停止录音指令');
                }

            } catch (error) {
                log('停止录音失败: ' + error.message);
            }
        }

        // 音频播放功能
        function handleAudioData(audioBlob) {
            log(`收到音频数据: ${audioBlob.size} bytes`);

            // 添加到播放队列
            audioQueue.push(audioBlob);

            // 如果没有在播放，开始播放
            if (!isPlaying) {
                playNextAudio();
            }
        }

        function playNextAudio() {
            if (audioQueue.length === 0) {
                isPlaying = false;
                document.getElementById('audioStatus').textContent = '播放完成';
                return;
            }

            const audioBlob = audioQueue.shift();
            const audioPlayer = document.getElementById('audioPlayer');

            // 创建音频URL
            const audioUrl = URL.createObjectURL(audioBlob);
            audioPlayer.src = audioUrl;

            isPlaying = true;
            document.getElementById('audioStatus').textContent = '正在播放...';

            // 播放音频
            audioPlayer.play().then(() => {
                log('开始播放音频');
            }).catch(error => {
                log('播放失败: ' + error.message);
                playNextAudio(); // 播放下一个
            });

            // 播放结束后清理并播放下一个
            audioPlayer.onended = () => {
                URL.revokeObjectURL(audioUrl);
                playNextAudio();
            };
        }

        // 日志功能
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('logArea');
            logArea.value += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logArea').value = '';
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (isRecording) {
                stopVoice();
            }
            if (websocket) {
                websocket.close();
            }
        });
    </script>
</body>
</html>

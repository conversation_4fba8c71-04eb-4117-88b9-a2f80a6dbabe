<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI小熊聊天</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .container {
            display: flex;
            width: 90%;
            max-width: 1200px;
            height: 80vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .bear-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .bear-container {
            position: relative;
            margin-bottom: 40px;
        }

        .bear {
            width: 300px;
            height: 300px;
            background: #D2B48C;
            border-radius: 50%;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .bear-image {
            max-width: 400px;
            max-height: 400px;
            width: auto;
            height: auto;
        }



        .status-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            top: 20px;
            right: 20px;
            animation: pulse 2s infinite;
        }

        .status-indicator.listening {
            background: #2196F3;
        }

        .status-indicator.thinking {
            background: #FF9800;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .switch-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .switch-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .chat-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }



        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            max-width: 80%;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message.bear {
            align-self: flex-start;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            flex-shrink: 0;
            overflow: hidden;
        }

        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .message-content {
            background: #2196F3;
            color: white;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.bear .message-content {
            background: #f0f0f0;
            color: #333;
        }

        /* 音频消息样式 */
        .audio-message {
            background: transparent !important;
            padding: 0 !important;
        }

        .audio-play-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            border: none;
            border-radius: 18px;
            background: #2196F3;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            justify-content: center;
        }

        .message.bear .audio-play-btn {
            background: #f0f0f0;
            color: #333;
        }

        .audio-play-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .audio-play-btn.playing {
            background: #FF5722;
            animation: pulse 1.5s infinite;
        }

        .message.bear .audio-play-btn.playing {
            background: #FF5722;
            color: white;
        }

        .audio-icon {
            width: 16px;
            height: 16px;
        }

        .audio-text {
            font-weight: 500;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            background: white;
            border-radius: 25px;
            padding: 12px 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .text-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            background: transparent;
        }

        .voice-btn {
            width: 50px;
            height: 50px;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            margin-left: 10px;
        }

        .voice-btn img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .voice-btn:hover {
            transform: scale(1.05);
        }

        .voice-btn.recording {
            animation: pulse 1s infinite;
        }

        .send-btn {
            width: 50px;
            height: 50px;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .send-btn img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #2196F3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #f44336;
            font-size: 12px;
            margin-top: 5px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                width: 95%;
                height: 95vh;
            }

            .bear-section {
                flex: 0 0 200px;
            }

            .bear {
                width: 150px;
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧小熊区域 -->
        <div class="bear-section">
            <div class="bear-container">
                <img src="img/bear_front.png" alt="AI小熊" class="bear-image" id="bear">
                <div class="status-indicator" id="statusIndicator"></div>
            </div>
            <button class="switch-btn" onclick="switchMode()">
                <img src="img/btn_switch.png" alt="切换背景" style="width: 20px; height: 20px; margin-right: 8px;">
                切换背景
            </button>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <div style="font-size: 24px;">🐻</div>
                    <div>
                        <div style="font-weight: bold; font-size: 18px;">AI小熊</div>
                        <div style="font-size: 12px; color: #666;">在线</div>
                    </div>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <!-- 聊天消息将在这里动态添加 -->
            </div>

            <div class="chat-input">
                <div class="input-container">
                    <input type="text" class="text-input" id="textInput" placeholder="输入消息..."
                           onkeypress="handleKeyPress(event)">
                </div>
                <button class="voice-btn" id="voiceBtn" onclick="toggleRecording()">
                    <img src="img/btn_send_voice.png" alt="发送语音">
                </button>
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <img src="img/btn_send_text.png" alt="发送文字">
                </button>
            </div>

            <div class="error-message" id="errorMessage"></div>
        </div>
    </div>

    <!-- 引入音频处理模块 -->
    <script src="js/audio-processor.js"></script>

    <script>
        // 全局变量
        let audioProcessor = null;
        let currentBgIndex = 0;

        const backgrounds = [
            'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        ];

        // API配置
        const API_BASE_URL = 'http://localhost:8712';  // 根据实际情况修改

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化音频处理器
            if (AudioProcessor.isSupported()) {
                audioProcessor = new AudioProcessor();
            } else {
                console.error('浏览器不支持录音功能');
                showError('浏览器不支持录音功能');
            }


        });

        // 页面卸载时清理音频资源
        window.addEventListener('beforeunload', () => {
            // 暂停所有音频
            pauseAllAudio();

            // 清理音频URL
            if (window.audioStorage) {
                Object.values(window.audioStorage).forEach(audio => {
                    if (audio.url) {
                        URL.revokeObjectURL(audio.url);
                    }
                });
                window.audioStorage = {};
            }

            // 清理音频处理器
            if (audioProcessor) {
                audioProcessor.destroy();
            }
        });



        // 切换背景
        function switchMode() {
            currentBgIndex = (currentBgIndex + 1) % backgrounds.length;
            document.body.style.background = backgrounds[currentBgIndex];
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 发送消息
        async function sendMessage() {
            const textInput = document.getElementById('textInput');
            const text = textInput.value.trim();

            if (!text) return;

            // 添加用户文字消息到聊天
            addMessage('user', text, 'text');
            textInput.value = '';

            // 设置状态为思考中
            setStatus('thinking');

            try {
                const response = await fetch(`${API_BASE_URL}/talk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: text })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // 获取音频数据
                const audioBlob = await response.blob();

                // 添加AI音频回复到聊天
                addMessage('bear', audioBlob, 'audio');

            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('bear', '抱歉，我现在无法回复，请稍后再试。', 'text');
                showError('发送失败，请检查网络连接');
            } finally {
                setStatus('idle');
            }
        }

        // 切换录音状态
        async function toggleRecording() {
            if (!audioProcessor) {
                showError('音频处理器未初始化');
                return;
            }

            if (!audioProcessor.getRecordingState()) {
                await startRecording();
            } else {
                await stopRecording();
            }
        }

        // 开始录音
        async function startRecording() {
            try {
                await audioProcessor.startRecording();

                // 更新UI
                const voiceBtn = document.getElementById('voiceBtn');
                voiceBtn.classList.add('recording');
                // 保持图片不变，只添加recording类用于动画效果
                setStatus('listening');

            } catch (error) {
                console.error('录音失败:', error);
                showError(error.message || '无法访问麦克风，请检查权限设置');
            }
        }

        // 停止录音
        async function stopRecording() {
            try {
                const audioBlob = await audioProcessor.stopRecording();

                // 更新UI
                const voiceBtn = document.getElementById('voiceBtn');
                voiceBtn.classList.remove('recording');
                // 保持图片不变，只移除recording类
                setStatus('thinking');

                // 发送音频消息
                await sendAudioMessage(audioBlob);

            } catch (error) {
                console.error('停止录音失败:', error);
                showError('录音处理失败');

                // 重置UI状态
                const voiceBtn = document.getElementById('voiceBtn');
                voiceBtn.classList.remove('recording');
                // 保持图片不变，只移除recording类
                setStatus('idle');
            }
        }



        // 发送音频消息
        async function sendAudioMessage(audioBlob) {
            try {
                // 验证音频数据
                if (!AudioUtils.isValidAudioBlob(audioBlob)) {
                    throw new Error('无效的音频数据');
                }

                // 转换为base64
                const base64Audio = await AudioUtils.blobToBase64(audioBlob);

                // 添加用户语音消息（显示播放按钮）
                addMessage('user', audioBlob, 'audio');

                const response = await fetch(`${API_BASE_URL}/talk`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ audio: base64Audio })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // 获取音频数据
                const responseAudioBlob = await response.blob();

                // 添加AI音频回复到聊天
                addMessage('bear', responseAudioBlob, 'audio');

            } catch (error) {
                console.error('发送语音消息失败:', error);
                addMessage('bear', '抱歉，语音识别失败，请重试。', 'text');
                showError('语音发送失败，请检查网络连接');
            } finally {
                setStatus('idle');
            }
        }



        // 添加消息到聊天
        function addMessage(sender, content, type = 'text') {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            if (sender==='bear'){
                messageDiv.className = `message`;
            }

            const avatarSrc = sender === 'user' ? 'img/user_avatar.png' : 'img/bear_avatar.png';
            const avatarAlt = sender === 'user' ? '用户头像' : 'AI小熊头像';

            let messageContent = '';

            if (type === 'text') {
                // 文本消息
                messageContent = `<div class="message-content">${content}</div>`;
            } else if (type === 'audio') {
                // 音频消息 - 显示播放按钮
                const audioId = 'audio_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const buttonId = 'btn_' + audioId;

                // 创建音频URL
                const audioUrl = URL.createObjectURL(content);

                // 存储音频数据供播放使用
                window.audioStorage = window.audioStorage || {};
                window.audioStorage[audioId] = {
                    url: audioUrl,
                    blob: content
                };

                const iconSrc = sender === 'user' ? 'img/user_voice_icon.png' : 'img/bear_voice_icon.png';
                const iconAlt = sender === 'user' ? '用户语音' : 'AI语音';
                messageContent = `
                    <div class="message-content audio-message">
                        <button class="audio-play-btn" id="${buttonId}" onclick="toggleAudioPlay('${audioId}', '${buttonId}')">
                            <img src="${iconSrc}" alt="${iconAlt}" class="audio-icon">
                            <span class="audio-text">${sender === 'user' ? '播放语音' : '播放回复'}</span>
                        </button>
                        <audio id="${audioId}" preload="metadata">
                            <source src="${audioUrl}" type="audio/mpeg">
                        </audio>
                    </div>
                `;
            }

            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <img src="${avatarSrc}" alt="${avatarAlt}">
                </div>
                ${messageContent}
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 如果是AI音频回复，自动播放
            if (sender === 'bear' && type === 'audio') {
                // 延迟一点时间确保DOM元素已经渲染
                setTimeout(() => {
                    const audioId = messageDiv.querySelector('audio').id;
                    const buttonId = messageDiv.querySelector('.audio-play-btn').id;
                    toggleAudioPlay(audioId, buttonId);
                }, 100);
            }
        }

        // 音频播放控制
        function toggleAudioPlay(audioId, buttonId) {
            const audio = document.getElementById(audioId);
            const button = document.getElementById(buttonId);
            const icon = button.querySelector('.audio-icon');
            const text = button.querySelector('.audio-text');

            if (!audio || !button) {
                console.error('Audio element or button not found');
                return;
            }

            if (audio.paused) {
                // 暂停其他正在播放的音频
                pauseAllAudio();

                // 播放当前音频
                audio.play().then(() => {
                    button.classList.add('playing');
                    text.textContent = '播放中...';
                }).catch(error => {
                    console.error('播放失败:', error);
                    showError('音频播放失败');
                });

                // 监听播放结束事件
                audio.onended = () => {
                    button.classList.remove('playing');
                    const originalText = button.closest('.message.user') ? '播放语音' : '播放回复';
                    text.textContent = originalText;
                };

            } else {
                // 暂停播放
                audio.pause();
                button.classList.remove('playing');
                const originalText = button.closest('.message.user') ? '播放语音' : '播放回复';
                text.textContent = originalText;
            }
        }

        // 暂停所有音频
        function pauseAllAudio() {
            const allAudios = document.querySelectorAll('audio');
            const allButtons = document.querySelectorAll('.audio-play-btn');

            allAudios.forEach(audio => {
                if (!audio.paused) {
                    audio.pause();
                }
            });

            allButtons.forEach(button => {
                button.classList.remove('playing');
                const text = button.querySelector('.audio-text');
                const originalText = button.closest('.message.user') ? '播放语音' : '播放回复';
                if (text) text.textContent = originalText;
            });
        }

        // 设置状态
        function setStatus(status) {
            const statusIndicator = document.getElementById('statusIndicator');

            statusIndicator.className = 'status-indicator';

            switch (status) {
                case 'listening':
                    statusIndicator.classList.add('listening');
                    break;
                case 'thinking':
                    statusIndicator.classList.add('thinking');
                    break;
                case 'idle':
                default:
                    // 默认状态
                    break;
            }
        }



        // 显示错误信息
        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            setTimeout(() => {
                errorElement.textContent = '';
            }, 3000);
        }
    </script>
</body>
</html>

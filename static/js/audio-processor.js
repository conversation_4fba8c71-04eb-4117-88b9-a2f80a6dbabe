/**
 * 音频处理工具类
 * 负责录音、格式转换、音频处理等功能
 */
class AudioProcessor {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.stream = null;
    }

    /**
     * 检查浏览器是否支持录音功能
     * @returns {boolean}
     */
    static isSupported() {
        return !!(navigator.mediaDevices && 
                 navigator.mediaDevices.getUserMedia && 
                 window.MediaRecorder);
    }

    /**
     * 开始录音
     * @returns {Promise<boolean>} 是否成功开始录音
     */
    async startRecording() {
        if (this.isRecording) {
            console.warn('Already recording');
            return false;
        }

        try {
            // 请求音频流，指定约束条件
            this.stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    sampleRate: 16000,      // 16kHz采样率
                    channelCount: 1,        // 单声道
                    sampleSize: 16,         // 16位深度
                    echoCancellation: true, // 回声消除
                    noiseSuppression: true  // 噪声抑制
                } 
            });
            
            // 创建MediaRecorder，尝试使用WAV格式
            const options = { mimeType: 'audio/wav' };
            if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                // 如果不支持WAV，尝试WebM
                options.mimeType = 'audio/webm';
                if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                    // 使用默认格式
                    delete options.mimeType;
                }
            }
            
            this.mediaRecorder = new MediaRecorder(this.stream, options);
            this.audioChunks = [];

            // 设置事件监听器
            this.mediaRecorder.ondataavailable = (event) => {
                this.audioChunks.push(event.data);
            };

            this.mediaRecorder.onstop = async () => {
                // 处理录制的音频
                await this._processRecordedAudio();
            };

            this.mediaRecorder.start();
            this.isRecording = true;
            
            console.log('Recording started with format:', this.mediaRecorder.mimeType);
            return true;
            
        } catch (error) {
            console.error('Failed to start recording:', error);
            this._cleanup();
            throw new Error('无法访问麦克风，请检查权限设置');
        }
    }

    /**
     * 停止录音
     * @returns {Promise<Blob>} 处理后的音频Blob
     */
    async stopRecording() {
        return new Promise((resolve, reject) => {
            if (!this.isRecording || !this.mediaRecorder) {
                reject(new Error('Not currently recording'));
                return;
            }

            // 设置停止后的回调
            this.mediaRecorder.onstop = async () => {
                try {
                    const audioBlob = await this._processRecordedAudio();
                    this._cleanup();
                    resolve(audioBlob);
                } catch (error) {
                    this._cleanup();
                    reject(error);
                }
            };

            this.mediaRecorder.stop();
            this.isRecording = false;
        });
    }

    /**
     * 处理录制的音频（内部方法）
     * @private
     * @returns {Promise<Blob>}
     */
    async _processRecordedAudio() {
        try {
            // 创建音频上下文进行格式转换
            const audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000  // 设置为16kHz
            });
            
            // 创建原始音频blob
            const originalBlob = new Blob(this.audioChunks, { 
                type: this.mediaRecorder.mimeType || 'audio/wav' 
            });
            
            // 转换为ArrayBuffer
            const arrayBuffer = await originalBlob.arrayBuffer();
            
            // 解码音频数据
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            
            // 转换为16kHz单声道PCM
            const pcmData = this._convertToPCM16(audioBuffer);
            
            // 创建WAV文件
            const wavBlob = this._createWAVBlob(pcmData, 16000, 1);
            
            console.log('Audio processed successfully:', {
                originalSize: originalBlob.size,
                processedSize: wavBlob.size,
                duration: pcmData.length / 16000
            });
            
            return wavBlob;
            
        } catch (error) {
            console.error('Audio processing failed:', error);
            // 如果转换失败，使用原始录音
            const fallbackBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
            console.warn('Using fallback audio blob');
            return fallbackBlob;
        }
    }

    /**
     * 转换为PCM 16bit格式（内部方法）
     * @private
     * @param {AudioBuffer} audioBuffer
     * @returns {Int16Array}
     */
    _convertToPCM16(audioBuffer) {
        // 获取第一个声道的数据（转为单声道）
        const channelData = audioBuffer.getChannelData(0);
        
        // 转换为16bit PCM
        const pcmData = new Int16Array(channelData.length);
        for (let i = 0; i < channelData.length; i++) {
            // 将float32 (-1到1) 转换为int16 (-32768到32767)
            const sample = Math.max(-1, Math.min(1, channelData[i]));
            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }
        
        return pcmData;
    }

    /**
     * 创建WAV文件（内部方法）
     * @private
     * @param {Int16Array} pcmData
     * @param {number} sampleRate
     * @param {number} channels
     * @returns {Blob}
     */
    _createWAVBlob(pcmData, sampleRate, channels) {
        const length = pcmData.length;
        const buffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(buffer);
        
        // WAV文件头写入函数
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        // 写入WAV文件头 (44字节)
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);                           // PCM格式
        view.setUint16(20, 1, true);                            // 音频格式
        view.setUint16(22, channels, true);                     // 声道数
        view.setUint32(24, sampleRate, true);                   // 采样率
        view.setUint32(28, sampleRate * channels * 2, true);    // 字节率
        view.setUint16(32, channels * 2, true);                 // 块对齐
        view.setUint16(34, 16, true);                           // 位深度
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);                   // 数据大小
        
        // 写入PCM数据
        let offset = 44;
        for (let i = 0; i < length; i++) {
            view.setInt16(offset, pcmData[i], true);
            offset += 2;
        }
        
        return new Blob([buffer], { type: 'audio/wav' });
    }

    /**
     * 清理资源（内部方法）
     * @private
     */
    _cleanup() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
    }

    /**
     * 获取当前录音状态
     * @returns {boolean}
     */
    getRecordingState() {
        return this.isRecording;
    }

    /**
     * 销毁实例，清理所有资源
     */
    destroy() {
        if (this.isRecording) {
            this.mediaRecorder.stop();
        }
        this._cleanup();
    }

    /**
     * 开始WebSocket流式录音
     * @param {WebSocket} websocket - WebSocket连接
     * @param {number} chunkInterval - 发送间隔(ms)
     */
    async startStreamingToWebSocket(websocket, chunkInterval = 200) {
        if (!AudioProcessor.isSupported()) {
            throw new Error('浏览器不支持录音功能');
        }

        if (this._isRecording) {
            throw new Error('录音已在进行中');
        }

        try {
            // 获取麦克风权限
            this._stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // 创建MediaRecorder
            this._mediaRecorder = new MediaRecorder(this._stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this._audioChunks = [];
            this._isRecording = true;

            // 设置数据处理
            this._mediaRecorder.ondataavailable = async (event) => {
                if (event.data.size > 0) {
                    this._audioChunks.push(event.data);

                    // 转换为PCM并发送
                    try {
                        const pcmData = await this._convertToPCM(event.data);
                        if (websocket.readyState === WebSocket.OPEN) {
                            websocket.send(pcmData);
                        }
                    } catch (error) {
                        console.error('音频转换失败:', error);
                    }
                }
            };

            // 开始录音
            this._mediaRecorder.start(chunkInterval);
            console.log('WebSocket流式录音已开始');

        } catch (error) {
            this._cleanup();
            throw new Error(`无法开始录音: ${error.message}`);
        }
    }

    /**
     * 停止WebSocket流式录音
     */
    async stopStreamingToWebSocket() {
        if (!this._isRecording) {
            return;
        }

        return new Promise((resolve) => {
            this._mediaRecorder.onstop = () => {
                this._cleanup();
                console.log('WebSocket流式录音已停止');
                resolve();
            };

            this._mediaRecorder.stop();
        });
    }

    /**
     * 转换音频数据为PCM格式
     * @private
     */
    async _convertToPCM(audioBlob) {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });

            const arrayBuffer = await audioBlob.arrayBuffer();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

            // 转换为16bit PCM
            const channelData = audioBuffer.getChannelData(0);
            const pcmData = new Int16Array(channelData.length);

            for (let i = 0; i < channelData.length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            }

            return pcmData.buffer;
        } catch (error) {
            console.error('PCM转换失败:', error);
            // 降级：返回原始数据
            return await audioBlob.arrayBuffer();
        }
    }
}

/**
 * 音频工具函数
 */
class AudioUtils {
    /**
     * 将Blob转换为Base64字符串
     * @param {Blob} blob
     * @returns {Promise<string>}
     */
    static blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1]; // 移除data:audio/wav;base64,前缀
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    /**
     * 获取音频时长（秒）
     * @param {Blob} audioBlob
     * @returns {Promise<number>}
     */
    static async getAudioDuration(audioBlob) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.onloadedmetadata = () => {
                resolve(audio.duration);
            };
            audio.onerror = reject;
            audio.src = URL.createObjectURL(audioBlob);
        });
    }

    /**
     * 验证音频格式
     * @param {Blob} audioBlob
     * @returns {boolean}
     */
    static isValidAudioBlob(audioBlob) {
        return audioBlob && 
               audioBlob instanceof Blob && 
               audioBlob.size > 0 && 
               audioBlob.type.startsWith('audio/');
    }
}

// 导出类（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AudioProcessor, AudioUtils };
}

// 全局暴露（用于直接在HTML中引用）
if (typeof window !== 'undefined') {
    window.AudioProcessor = AudioProcessor;
    window.AudioUtils = AudioUtils;
}

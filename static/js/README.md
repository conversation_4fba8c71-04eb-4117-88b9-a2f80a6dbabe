# 音频处理模块说明

## 📁 **文件结构**

```
static/js/
├── audio-processor.js    # 音频处理核心模块
└── README.md            # 本说明文档
```

## 🎯 **模块概述**

`audio-processor.js` 是一个独立的音频处理模块，负责：
- 录音功能
- 音频格式转换（转为16kHz单声道16bit PCM WAV）
- 音频数据处理
- 浏览器兼容性处理

## 📚 **API文档**

### **AudioProcessor 类**

#### 构造函数
```javascript
const audioProcessor = new AudioProcessor();
```

#### 静态方法
```javascript
// 检查浏览器支持
AudioProcessor.isSupported() // returns boolean
```

#### 实例方法

##### `startRecording()`
开始录音
```javascript
await audioProcessor.startRecording();
```
- **返回**: `Promise<boolean>` - 是否成功开始录音
- **异常**: 抛出错误信息

##### `stopRecording()`
停止录音并返回处理后的音频
```javascript
const audioBlob = await audioProcessor.stopRecording();
```
- **返回**: `Promise<Blob>` - 处理后的WAV音频Blob
- **异常**: 抛出错误信息

##### `getRecordingState()`
获取当前录音状态
```javascript
const isRecording = audioProcessor.getRecordingState();
```
- **返回**: `boolean` - 是否正在录音

##### `destroy()`
销毁实例，清理资源
```javascript
audioProcessor.destroy();
```

### **AudioUtils 类**

#### 静态方法

##### `blobToBase64(blob)`
将Blob转换为Base64字符串
```javascript
const base64 = await AudioUtils.blobToBase64(audioBlob);
```
- **参数**: `Blob` - 音频Blob对象
- **返回**: `Promise<string>` - Base64字符串（不含前缀）

##### `getAudioDuration(audioBlob)`
获取音频时长
```javascript
const duration = await AudioUtils.getAudioDuration(audioBlob);
```
- **参数**: `Blob` - 音频Blob对象
- **返回**: `Promise<number>` - 音频时长（秒）

##### `isValidAudioBlob(audioBlob)`
验证音频Blob是否有效
```javascript
const isValid = AudioUtils.isValidAudioBlob(audioBlob);
```
- **参数**: `Blob` - 音频Blob对象
- **返回**: `boolean` - 是否为有效音频

## 🔧 **使用示例**

### **基本录音流程**
```javascript
// 1. 初始化
const audioProcessor = new AudioProcessor();

// 2. 检查支持
if (!AudioProcessor.isSupported()) {
    console.error('浏览器不支持录音');
    return;
}

// 3. 开始录音
try {
    await audioProcessor.startRecording();
    console.log('录音开始');
} catch (error) {
    console.error('录音失败:', error.message);
}

// 4. 停止录音
try {
    const audioBlob = await audioProcessor.stopRecording();
    console.log('录音完成，音频大小:', audioBlob.size);
    
    // 5. 转换为Base64
    const base64Audio = await AudioUtils.blobToBase64(audioBlob);
    
    // 6. 发送到服务器
    // ... 发送逻辑
    
} catch (error) {
    console.error('停止录音失败:', error.message);
}

// 7. 清理资源
audioProcessor.destroy();
```

### **在HTML中使用**
```html
<!-- 引入模块 -->
<script src="js/audio-processor.js"></script>

<script>
let audioProcessor = null;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    if (AudioProcessor.isSupported()) {
        audioProcessor = new AudioProcessor();
    } else {
        alert('浏览器不支持录音功能');
    }
});

// 录音按钮事件
async function toggleRecording() {
    if (!audioProcessor.getRecordingState()) {
        await audioProcessor.startRecording();
        // 更新UI...
    } else {
        const audioBlob = await audioProcessor.stopRecording();
        // 处理音频...
    }
}
</script>
```

## 🎵 **音频格式规格**

### **输出格式**
- **容器**: WAV
- **编码**: PCM (未压缩)
- **采样率**: 16kHz
- **位深度**: 16bit
- **声道**: 单声道 (Mono)
- **字节序**: Little Endian

### **文件结构**
```
WAV文件 = WAV头部(44字节) + PCM数据
```

### **兼容性**
- ✅ 字节跳动ASR API
- ✅ 大部分语音识别服务
- ✅ 标准音频播放器

## 🔍 **错误处理**

### **常见错误**

#### 1. 麦克风权限被拒绝
```javascript
// 错误信息: "无法访问麦克风，请检查权限设置"
// 解决方案: 用户需要在浏览器中允许麦克风权限
```

#### 2. 浏览器不支持
```javascript
// 检查方法
if (!AudioProcessor.isSupported()) {
    // 显示不支持提示
}
```

#### 3. 音频处理失败
```javascript
// 自动降级到原始录音格式
// 日志: "Using fallback audio blob"
```

## 🌐 **浏览器兼容性**

### **支持的浏览器**
- ✅ Chrome 47+
- ✅ Firefox 29+
- ✅ Safari 14+
- ✅ Edge 79+

### **移动端支持**
- ✅ iOS Safari 14+
- ✅ Android Chrome 47+
- ⚠️ 需要HTTPS环境

### **功能支持检查**
```javascript
// 检查MediaRecorder支持
const hasMediaRecorder = typeof MediaRecorder !== 'undefined';

// 检查getUserMedia支持
const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

// 检查Web Audio API支持
const hasWebAudio = !!(window.AudioContext || window.webkitAudioContext);
```

## 🚀 **性能优化**

### **内存管理**
- 录音完成后自动清理音频块
- 停止录音时释放媒体流
- 提供destroy方法清理所有资源

### **处理优化**
- 使用Web Audio API进行高效格式转换
- 支持音频处理失败时的降级方案
- 异步处理避免阻塞UI

## 🔧 **自定义配置**

如需修改音频参数，可以编辑 `audio-processor.js` 中的相关配置：

```javascript
// 录音约束
audio: {
    sampleRate: 16000,      // 采样率
    channelCount: 1,        // 声道数
    sampleSize: 16,         // 位深度
    echoCancellation: true, // 回声消除
    noiseSuppression: true  // 噪声抑制
}

// AudioContext配置
const audioContext = new AudioContext({
    sampleRate: 16000  // 输出采样率
});
```

## 📝 **注意事项**

1. **HTTPS要求**: 录音功能需要在HTTPS环境下使用
2. **用户交互**: 某些浏览器需要用户手势触发录音
3. **权限提示**: 首次使用会弹出麦克风权限请求
4. **资源清理**: 使用完毕后调用destroy方法清理资源
5. **错误处理**: 建议包装try-catch处理异常情况

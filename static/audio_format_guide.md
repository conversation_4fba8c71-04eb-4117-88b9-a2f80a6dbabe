# 音频格式处理说明

## 🎯 **问题背景**

HTML的 `MediaRecorder` API 默认录制的音频格式与字节跳动ASR API的要求不匹配：

### 📊 **格式对比**

| 项目 | HTML MediaRecorder (默认) | ASR API要求 |
|------|--------------------------|-------------|
| **格式** | WebM/OGG (浏览器决定) | PCM WAV |
| **采样率** | 48kHz | **16kHz** |
| **位深度** | 32bit float | **16bit** |
| **声道** | 立体声 | **单声道** |
| **编码** | 压缩格式 | **原始PCM** |

## ✅ **解决方案**

### 1. **录音约束设置**
```javascript
const stream = await navigator.mediaDevices.getUserMedia({ 
    audio: {
        sampleRate: 16000,      // 16kHz采样率
        channelCount: 1,        // 单声道
        sampleSize: 16,         // 16位深度
        echoCancellation: true, // 回声消除
        noiseSuppression: true  // 噪声抑制
    } 
});
```

### 2. **音频格式转换**
```javascript
// 使用Web Audio API进行格式转换
const audioContext = new AudioContext({ sampleRate: 16000 });
const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
const pcmData = convertToPCM16(audioBuffer);
const wavBlob = createWAVBlob(pcmData, 16000, 1);
```

### 3. **PCM转换函数**
```javascript
function convertToPCM16(audioBuffer) {
    const channelData = audioBuffer.getChannelData(0); // 取第一声道
    const pcmData = new Int16Array(channelData.length);
    
    for (let i = 0; i < channelData.length; i++) {
        // float32 (-1到1) → int16 (-32768到32767)
        const sample = Math.max(-1, Math.min(1, channelData[i]));
        pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }
    
    return pcmData;
}
```

### 4. **WAV文件头生成**
```javascript
function createWAVBlob(pcmData, sampleRate, channels) {
    const buffer = new ArrayBuffer(44 + pcmData.length * 2);
    const view = new DataView(buffer);
    
    // 标准WAV文件头 (44字节)
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + pcmData.length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);           // PCM格式
    view.setUint16(20, 1, true);            // 音频格式
    view.setUint16(22, channels, true);     // 声道数
    view.setUint32(24, sampleRate, true);   // 采样率
    view.setUint32(28, sampleRate * channels * 2, true); // 字节率
    view.setUint16(32, channels * 2, true); // 块对齐
    view.setUint16(34, 16, true);           // 位深度
    writeString(36, 'data');
    view.setUint32(40, pcmData.length * 2, true); // 数据大小
    
    // 写入PCM数据
    // ...
}
```

## 🔧 **技术细节**

### **采样率转换**
- **原理**: 使用Web Audio API的 `AudioContext` 重采样
- **目标**: 48kHz → 16kHz
- **方法**: 创建16kHz的AudioContext进行自动重采样

### **位深度转换**
- **原理**: Float32 → Int16 数值映射
- **范围**: [-1.0, 1.0] → [-32768, 32767]
- **公式**: `sample < 0 ? sample * 0x8000 : sample * 0x7FFF`

### **声道转换**
- **方法**: 只取第一个声道数据 `audioBuffer.getChannelData(0)`
- **效果**: 立体声 → 单声道

### **格式封装**
- **容器**: 标准WAV格式
- **头部**: 44字节WAV文件头
- **数据**: 16bit PCM原始音频数据

## 🎯 **ASR API要求**

根据字节跳动ASR API文档，音频参数要求：

```python
"audio": {
    "format": "pcm",    # PCM格式
    "codec": "raw",     # 原始编码
    "rate": 16000,      # 16kHz采样率
    "bits": 16,         # 16位深度
    "channel": 1        # 单声道
}
```

## 🚀 **优化建议**

### 1. **浏览器兼容性**
```javascript
// 检查MediaRecorder支持的格式
const options = { mimeType: 'audio/wav' };
if (!MediaRecorder.isTypeSupported(options.mimeType)) {
    options.mimeType = 'audio/webm';
}
```

### 2. **错误处理**
```javascript
try {
    // 音频转换逻辑
    const wavBlob = createWAVBlob(pcmData, 16000, 1);
    await sendAudioMessage(wavBlob);
} catch (error) {
    // 转换失败时使用原始录音
    const fallbackBlob = new Blob(audioChunks, { type: 'audio/wav' });
    await sendAudioMessage(fallbackBlob);
}
```

### 3. **性能优化**
- 使用 `AudioWorklet` 进行实时处理
- 分块处理大音频文件
- 缓存AudioContext实例

## 📱 **移动端注意事项**

### **iOS Safari**
- 需要用户手势触发录音
- AudioContext需要在用户交互后创建
- 某些约束可能不被支持

### **Android Chrome**
- 支持大部分Web Audio API
- 录音权限需要HTTPS
- 性能相对较好

## 🔍 **调试方法**

### 1. **检查录音参数**
```javascript
console.log('MediaRecorder mimeType:', mediaRecorder.mimeType);
console.log('AudioContext sampleRate:', audioContext.sampleRate);
console.log('AudioBuffer channels:', audioBuffer.numberOfChannels);
```

### 2. **验证音频格式**
```javascript
console.log('PCM data length:', pcmData.length);
console.log('WAV blob size:', wavBlob.size);
console.log('Expected duration:', pcmData.length / 16000, 'seconds');
```

### 3. **测试音频质量**
- 录制短音频测试
- 检查ASR识别准确率
- 对比不同格式的效果

## 📝 **总结**

通过以上处理，HTML客户端现在可以：
- ✅ 录制16kHz单声道16bit PCM音频
- ✅ 生成标准WAV格式文件
- ✅ 兼容字节跳动ASR API要求
- ✅ 提供错误处理和降级方案

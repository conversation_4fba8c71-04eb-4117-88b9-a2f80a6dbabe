<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频处理模块测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        .button:hover {
            background: #45a049;
        }
        
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .recording {
            background: #f44336 !important;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .audio-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .audio-info div {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🎤 音频处理模块测试</h1>
    
    <!-- 基础功能测试 -->
    <div class="test-container">
        <h2 class="test-title">基础功能测试</h2>
        
        <div id="supportStatus" class="status"></div>
        
        <button id="recordBtn" class="button" onclick="toggleRecording()">
            🎤 开始录音
        </button>
        
        <button id="testBtn" class="button" onclick="runTests()">
            🧪 运行测试
        </button>
        
        <div id="recordingStatus" class="status" style="display: none;"></div>
        
        <div id="audioInfo" class="audio-info" style="display: none;">
            <h4>音频信息:</h4>
            <div id="audioDetails"></div>
        </div>
        
        <audio id="audioPlayer" controls style="width: 100%; margin: 10px 0; display: none;"></audio>
    </div>
    
    <!-- 测试日志 -->
    <div class="test-container">
        <h2 class="test-title">测试日志</h2>
        <div id="testLog" class="log"></div>
        <button class="button" onclick="clearLog()">清空日志</button>
    </div>

    <!-- 引入音频处理模块 -->
    <script src="js/audio-processor.js"></script>
    
    <script>
        let audioProcessor = null;
        let testResults = [];
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkSupport();
            initAudioProcessor();
        });
        
        // 检查浏览器支持
        function checkSupport() {
            const supportStatus = document.getElementById('supportStatus');
            
            if (AudioProcessor.isSupported()) {
                supportStatus.className = 'status success';
                supportStatus.textContent = '✅ 浏览器支持录音功能';
                log('浏览器支持检查: 通过');
            } else {
                supportStatus.className = 'status error';
                supportStatus.textContent = '❌ 浏览器不支持录音功能';
                log('浏览器支持检查: 失败');
                document.getElementById('recordBtn').disabled = true;
            }
        }
        
        // 初始化音频处理器
        function initAudioProcessor() {
            try {
                audioProcessor = new AudioProcessor();
                log('AudioProcessor 初始化成功');
            } catch (error) {
                log('AudioProcessor 初始化失败: ' + error.message);
            }
        }
        
        // 切换录音状态
        async function toggleRecording() {
            const recordBtn = document.getElementById('recordBtn');
            const recordingStatus = document.getElementById('recordingStatus');
            
            if (!audioProcessor.getRecordingState()) {
                // 开始录音
                try {
                    await audioProcessor.startRecording();
                    
                    recordBtn.textContent = '⏹️ 停止录音';
                    recordBtn.classList.add('recording');
                    
                    recordingStatus.className = 'status info';
                    recordingStatus.textContent = '🎤 正在录音...';
                    recordingStatus.style.display = 'block';
                    
                    log('录音开始');
                    
                } catch (error) {
                    log('录音开始失败: ' + error.message);
                    
                    recordingStatus.className = 'status error';
                    recordingStatus.textContent = '❌ 录音失败: ' + error.message;
                    recordingStatus.style.display = 'block';
                }
            } else {
                // 停止录音
                try {
                    const audioBlob = await audioProcessor.stopRecording();
                    
                    recordBtn.textContent = '🎤 开始录音';
                    recordBtn.classList.remove('recording');
                    
                    recordingStatus.className = 'status success';
                    recordingStatus.textContent = '✅ 录音完成';
                    
                    log('录音完成，音频大小: ' + audioBlob.size + ' bytes');
                    
                    // 显示音频信息
                    await displayAudioInfo(audioBlob);
                    
                    // 播放音频
                    playAudio(audioBlob);
                    
                } catch (error) {
                    log('录音停止失败: ' + error.message);
                    
                    recordingStatus.className = 'status error';
                    recordingStatus.textContent = '❌ 录音处理失败: ' + error.message;
                    
                    recordBtn.textContent = '🎤 开始录音';
                    recordBtn.classList.remove('recording');
                }
            }
        }
        
        // 显示音频信息
        async function displayAudioInfo(audioBlob) {
            const audioInfo = document.getElementById('audioInfo');
            const audioDetails = document.getElementById('audioDetails');
            
            try {
                const duration = await AudioUtils.getAudioDuration(audioBlob);
                const base64 = await AudioUtils.blobToBase64(audioBlob);
                const isValid = AudioUtils.isValidAudioBlob(audioBlob);
                
                audioDetails.innerHTML = `
                    <div><strong>文件大小:</strong> ${audioBlob.size} bytes</div>
                    <div><strong>文件类型:</strong> ${audioBlob.type}</div>
                    <div><strong>音频时长:</strong> ${duration.toFixed(2)} 秒</div>
                    <div><strong>Base64长度:</strong> ${base64.length} 字符</div>
                    <div><strong>有效性检查:</strong> ${isValid ? '✅ 有效' : '❌ 无效'}</div>
                `;
                
                audioInfo.style.display = 'block';
                
                log('音频信息获取成功');
                
            } catch (error) {
                log('音频信息获取失败: ' + error.message);
            }
        }
        
        // 播放音频
        function playAudio(audioBlob) {
            const audioPlayer = document.getElementById('audioPlayer');
            const audioUrl = URL.createObjectURL(audioBlob);
            
            audioPlayer.src = audioUrl;
            audioPlayer.style.display = 'block';
            
            // 清理URL对象
            audioPlayer.onended = () => {
                URL.revokeObjectURL(audioUrl);
            };
        }
        
        // 运行测试套件
        async function runTests() {
            log('=== 开始运行测试套件 ===');
            testResults = [];
            
            // 测试1: 类存在性检查
            test('AudioProcessor类存在', () => {
                return typeof AudioProcessor === 'function';
            });
            
            test('AudioUtils类存在', () => {
                return typeof AudioUtils === 'function';
            });
            
            // 测试2: 静态方法检查
            test('AudioProcessor.isSupported()方法', () => {
                return typeof AudioProcessor.isSupported === 'function';
            });
            
            test('AudioUtils.blobToBase64()方法', () => {
                return typeof AudioUtils.blobToBase64 === 'function';
            });
            
            test('AudioUtils.isValidAudioBlob()方法', () => {
                return typeof AudioUtils.isValidAudioBlob === 'function';
            });
            
            // 测试3: 实例方法检查
            if (audioProcessor) {
                test('audioProcessor.startRecording()方法', () => {
                    return typeof audioProcessor.startRecording === 'function';
                });
                
                test('audioProcessor.stopRecording()方法', () => {
                    return typeof audioProcessor.stopRecording === 'function';
                });
                
                test('audioProcessor.getRecordingState()方法', () => {
                    return typeof audioProcessor.getRecordingState === 'function';
                });
                
                test('audioProcessor.destroy()方法', () => {
                    return typeof audioProcessor.destroy === 'function';
                });
            }
            
            // 测试4: 工具函数测试
            test('AudioUtils.isValidAudioBlob() - 无效输入', () => {
                return !AudioUtils.isValidAudioBlob(null) && 
                       !AudioUtils.isValidAudioBlob(undefined) &&
                       !AudioUtils.isValidAudioBlob('invalid');
            });
            
            // 输出测试结果
            const passed = testResults.filter(r => r.passed).length;
            const total = testResults.length;
            
            log(`=== 测试完成: ${passed}/${total} 通过 ===`);
            
            if (passed === total) {
                log('🎉 所有测试通过！');
            } else {
                log('⚠️ 部分测试失败，请检查实现');
            }
        }
        
        // 测试辅助函数
        function test(name, testFn) {
            try {
                const result = testFn();
                const passed = !!result;
                
                testResults.push({ name, passed });
                log(`${passed ? '✅' : '❌'} ${name}: ${passed ? 'PASS' : 'FAIL'}`);
                
                return passed;
            } catch (error) {
                testResults.push({ name, passed: false, error: error.message });
                log(`❌ ${name}: ERROR - ${error.message}`);
                return false;
            }
        }
        
        // 日志函数
        function log(message) {
            const testLog = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            testLog.innerHTML += `[${timestamp}] ${message}\n`;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(message);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (audioProcessor) {
                audioProcessor.destroy();
            }
        });
    </script>
</body>
</html>

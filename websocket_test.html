<!DOCTYPE html>
<html>
<head>
    <title>AI Bear WebSocket Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        #messages { 
            border: 1px solid #ddd; 
            height: 400px; 
            overflow-y: scroll; 
            padding: 15px; 
            margin: 20px 0; 
            background: #fafafa;
            border-radius: 4px;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        input[type="text"] { 
            flex: 1;
            padding: 10px; 
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button { 
            padding: 10px 20px; 
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .message { 
            margin: 8px 0; 
            padding: 8px 12px; 
            border-radius: 6px;
            word-wrap: break-word;
        }
        
        .message.sent { 
            background: #e3f2fd; 
            border-left: 4px solid #2196f3;
        }
        
        .message.received { 
            background: #f1f8e9; 
            border-left: 4px solid #4caf50;
        }
        
        .message.system { 
            background: #fff3e0; 
            border-left: 4px solid #ff9800;
            font-style: italic;
        }
        
        .message.error { 
            background: #ffebee; 
            border-left: 4px solid #f44336;
            color: #c62828;
        }
        
        .timestamp {
            font-size: 12px;
            color: #666;
            margin-right: 10px;
        }
        
        .server-config {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .server-config label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .server-config input {
            width: 200px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐻 AI Bear WebSocket Test</h1>
        
        <div class="server-config">
            <div style="margin-bottom: 10px;">
                <label>服务器地址:</label>
                <input type="text" id="serverUrl" value="ws://localhost:8000/ws" />
            </div>
            <div>
                <label>客户端ID:</label>
                <input type="text" id="clientId" placeholder="可选，留空使用默认端点" />
            </div>
        </div>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>
        
        <div id="messages"></div>
        
        <div class="input-group">
            <input type="text" id="messageText" placeholder="输入消息..." />
            <button class="btn-primary" onclick="sendMessage()">发送</button>
        </div>
        
        <div class="controls">
            <button class="btn-success" onclick="connect()">连接</button>
            <button class="btn-danger" onclick="disconnect()">断开连接</button>
            <button class="btn-primary" onclick="clearMessages()">清空消息</button>
        </div>
    </div>
    
    <script>
        var ws = null;
        var isConnected = false;
        
        function updateStatus(status, isConnected) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = '状态: ' + status;
            statusElement.className = 'status ' + (isConnected ? 'connected' : 'disconnected');
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage("已经连接到服务器", "system");
                return;
            }
            
            const serverUrl = document.getElementById('serverUrl').value;
            const clientId = document.getElementById('clientId').value;
            
            let wsUrl = serverUrl;
            if (clientId) {
                // 如果有客户端ID，使用带ID的端点
                wsUrl = serverUrl.replace('/ws', `/ws/${clientId}`);
            }
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus("已连接", true);
                    addMessage(`已连接到服务器: ${wsUrl}`, "system");
                };
                
                ws.onmessage = function(event) {
                    addMessage("收到: " + event.data, "received");
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus("连接已关闭", false);
                    addMessage(`连接已关闭 (代码: ${event.code})`, "system");
                };
                
                ws.onerror = function(error) {
                    isConnected = false;
                    updateStatus("连接错误", false);
                    addMessage("连接错误: " + error, "error");
                };
                
                updateStatus("正在连接...", false);
                
            } catch (error) {
                addMessage("连接失败: " + error.message, "error");
                updateStatus("连接失败", false);
            }
        }
        
        function sendMessage() {
            const input = document.getElementById("messageText");
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage("请先连接到服务器", "error");
                return;
            }
            
            if (input.value.trim()) {
                try {
                    ws.send(input.value);
                    addMessage("发送: " + input.value, "sent");
                    input.value = "";
                } catch (error) {
                    addMessage("发送失败: " + error.message, "error");
                }
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            isConnected = false;
            updateStatus("已断开连接", false);
        }
        
        function clearMessages() {
            document.getElementById("messages").innerHTML = "";
        }
        
        function addMessage(message, type) {
            const messages = document.getElementById("messages");
            const messageElement = document.createElement("div");
            messageElement.className = "message " + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageElement.innerHTML = `<span class="timestamp">${timestamp}</span>${message}`;
            
            messages.appendChild(messageElement);
            messages.scrollTop = messages.scrollHeight;
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            addMessage("页面已加载，点击'连接'按钮开始", "system");
        };
        
        // 回车键发送消息
        document.getElementById("messageText").addEventListener("keypress", function(e) {
            if (e.key === "Enter") {
                sendMessage();
            }
        });
        
        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>

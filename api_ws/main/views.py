from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from utils.g import service_locator

# 创建WebSocket路由器
router = APIRouter()


@router.websocket("/main")
async def main(websocket: WebSocket):
    """
    业务入口，通信协议说明：
    1.支持的字符串指令(命令和参数之间用空格分割，参数与参数之间用||分割)：
        ClientStartCmd null:告诉服务端，客户端接下来要发送音频流
        ClientStopCmd null:告诉服务端，客户端停止发送音频流
        PressCmd part||pressure:告诉服务端，客户端按压了某个部位，part为部位，pressure为压力值
    2.如果客户端直接下发字节流，那么服务端就认定这是PCM 16bit 16kHz 单声道的音频数据。如果客户端在下发音频流之前没有发送ClientStartCmd指令，那么服务端会忽略这些数据。
    """
    manager = service_locator.client_manager
    client = await manager.connect(websocket)
    await client.start()
    await manager.disconnect(client.ws)

import base64
import io
import random

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Request, Body
from starlette.responses import StreamingResponse

import config
from core.llm.llm import BearLLM
from utils.asr.llm_asr import LLMAsrClient
from utils.g import service_locator
from utils.tts.tts import TTS

# 创建WebSocket路由器
router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点 - 处理客户端连接"""
    manager = service_locator.client_manager
    await manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            print(f"Received message: {data}")

            # 这里可以添加你的业务逻辑
            # 例如：STT -> LLM -> TTS 的处理流程

            # 简单的回声响应
            response = f"服务器收到消息: {data}"
            await manager.send_personal_message(response, websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        print(f"WebSocket error: {e}")
        manager.disconnect(websocket)


# 演示版，维护全局上下文
llm = BearLLM()
asr = LLMAsrClient(config.AUDIO_APP_ID, config.AUDIO_ACCESS_TOKEN)
tts = TTS(config.AUDIO_APP_ID, config.AUDIO_ACCESS_TOKEN)


async def handle_press(part, pressure):
    """
    处理按压
    """
    data_mapping = {
        "face": {
            1: {
                "sentences": [
                    "哎呀~你捏的人家脸都红啦！是不是想看我害羞的样子？",
                    "脸颊痒痒的~你在给我做脸部按摩吗？好舒服呀！",
                    "轻轻戳我的酒窝，是不是想逗我笑？那我要挠你痒痒报复啦！",
                    "脸脸被抚摸的感觉像阳光照在云朵上~今天有什么开心的事要分享吗？",
                    "你摸我的脸蛋时，我感觉心里开出了小花花"
                ],
                "mood": "愉悦",
            },
            2: {
                "sentences": [],
                "mood": "低落",
            },
            3: {
                "sentences": [],
                "mood": "抑郁",
            }
        },
        "neck": {
            1: {
                "sentences": [
                    "脖子痒痒的~你在给我挠痒痒吗？像小鸟在轻轻啄~",
                    "这样抚摸我的后颈，感觉像被微风拥抱~",
                    "你摸到我脖子上的绒毛啦！要给我系蝴蝶结吗？"
                ],
                "mood": "放松",
            },
            2: {
                "sentences": [],
                "mood": "紧张",
            },
            3: {
                "sentences": [],
                "mood": "窒息感",
            }
        }
    }
    data = data_mapping.get(part, {}).get(pressure, [])
    if data["sentences"]:
        # 如果配置了固定句式，那么直接用，如果没有那么就用大模型生成
        sentence = random.choice(data["sentences"])
        llm.append_context("system", f"用户按压了:{part},压力值:{pressure},小熊感到:{data['mood']}")
        llm.append_context("assistant", sentence)
        audio = await tts.sync_tts(sentence)
    else:
        resp = await llm.talk(f"用户按压了:{part},压力值:{pressure},小熊感到:{data['mood']}", "system")
        audio = await tts.sync_tts(resp)
    mp3_stream = io.BytesIO(audio)
    mp3_stream.seek(0)
    print("上下文：", llm.history)
    return StreamingResponse(
        mp3_stream,
        media_type="audio/mpeg",
        headers={"Content-Disposition": "inline"}
    )


@router.post("/talk")
async def talk_view(request: Request, text: str = Body(None, embed=True), audio: str = Body(None),
                    part: str = Body(None), pressure: int = Body(None)):
    """
    """
    if part:
        return await handle_press(part, pressure)
    if not text:
        audio = base64.b64decode(audio)
        text = await asr.sync_recognize(audio)

    resp = await llm.talk(text)
    audio = await tts.sync_tts(resp)
    mp3_stream = io.BytesIO(audio)
    mp3_stream.seek(0)
    return StreamingResponse(
        mp3_stream,
        media_type="audio/mpeg",
        headers={"Content-Disposition": "inline"}
    )

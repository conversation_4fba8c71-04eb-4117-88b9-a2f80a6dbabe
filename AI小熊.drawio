<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.0.9 Chrome/128.0.6613.186 Electron/32.2.5 Safari/537.36" version="26.0.9">
  <diagram name="第 1 页" id="_WeXplwPggY35RfvsVF-">
    <mxGraphModel dx="1208" dy="810" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="PIKVH5PZTBOvhKL6wvjP-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="PIKVH5PZTBOvhKL6wvjP-1" target="PIKVH5PZTBOvhKL6wvjP-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="240" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIKVH5PZTBOvhKL6wvjP-3" value="说话" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="PIKVH5PZTBOvhKL6wvjP-2" vertex="1" connectable="0">
          <mxGeometry x="-0.1045" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIKVH5PZTBOvhKL6wvjP-1" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="110" y="230" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ccJ2iv05-3ymu_GcZPlU-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PIKVH5PZTBOvhKL6wvjP-4" target="ccJ2iv05-3ymu_GcZPlU-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="g6p-Ca-htyM0wm7KPViV-1" value="同步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="ccJ2iv05-3ymu_GcZPlU-2">
          <mxGeometry x="-0.0833" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIKVH5PZTBOvhKL6wvjP-4" value="ASR" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="250" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ccJ2iv05-3ymu_GcZPlU-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ccJ2iv05-3ymu_GcZPlU-1" target="ccJ2iv05-3ymu_GcZPlU-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="g6p-Ca-htyM0wm7KPViV-2" value="异步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="ccJ2iv05-3ymu_GcZPlU-4">
          <mxGeometry x="-0.0333" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ccJ2iv05-3ymu_GcZPlU-1" value="大模型" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="450" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ccJ2iv05-3ymu_GcZPlU-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="ccJ2iv05-3ymu_GcZPlU-3" target="PIKVH5PZTBOvhKL6wvjP-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="710" y="390" as="targetPoint" />
            <Array as="points">
              <mxPoint x="710" y="380" />
              <mxPoint x="125" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="g6p-Ca-htyM0wm7KPViV-3" value="异步" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="ccJ2iv05-3ymu_GcZPlU-5">
          <mxGeometry x="-0.9416" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ccJ2iv05-3ymu_GcZPlU-3" value="TTS" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="650" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="g6p-Ca-htyM0wm7KPViV-4" value="Actor" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="500" width="30" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

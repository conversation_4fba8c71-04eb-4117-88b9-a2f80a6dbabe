from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from utils.g import service_locator
from core.ws_manager.manager import ConnectionManager
from api_ws.demo.views import router as ws_router
from api_ws.main.views import router as main_router

app = FastAPI(title="AI Bear WebSocket Server")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，生产环境建议指定具体域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)
service_locator.init(app)
service_locator.client_manager = ConnectionManager()

# 注册WebSocket路由
app.include_router(ws_router)
app.include_router(main_router)


@app.get("/")
async def root():
    """API信息页面"""
    return "ok"


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8712)

# 豆包大模型客户端封装

这是一个对豆包大模型API的Python封装，提供了同步和异步调用方式。

## 功能特性

- ✅ 同步和异步调用支持
- ✅ 流式响应支持
- ✅ 简单易用的API接口
- ✅ 完整的错误处理
- ✅ 便捷函数支持
- ✅ 单例模式支持

## 安装依赖

```bash
pip install volcengine-python-sdk[ark]
```

## 快速开始

### 1. 设置API Key

```python
import os
os.environ["ARK_API_KEY"] = "your-api-key-here"
```

或者直接在代码中传入：

```python
from utils.llm import DoubaoClient

client = DoubaoClient(api_key="your-api-key-here")
```

### 2. 同步调用

```python
from utils.llm import DoubaoClient

# 创建客户端
client = DoubaoClient()

# 简单聊天
response = client.simple_chat("你好，请介绍一下你自己")
print(response)

# 带系统消息的聊天
response = client.simple_chat(
    "请用中文回答问题",
    system_message="你是一个专业的AI助手"
)
print(response)

# 完整的聊天完成
messages = [
    {"role": "system", "content": "你是豆包AI助手"},
    {"role": "user", "content": "什么是人工智能？"}
]
result = client.chat_completion(messages)
print(result)
```

### 3. 异步调用

```python
import asyncio
from utils.llm import DoubaoClient

async def main():
    client = DoubaoClient()
    
    # 异步简单聊天
    response = await client.async_simple_chat("请介绍一下机器学习")
    print(response)
    
    # 异步完整聊天
    messages = [{"role": "user", "content": "解释一下深度学习"}]
    result = await client.async_chat_completion(messages)
    print(result)

asyncio.run(main())
```

### 4. 流式响应

```python
# 同步流式
messages = [{"role": "user", "content": "请写一首诗"}]
for chunk in client.stream_chat(messages):
    print(chunk, end="", flush=True)

# 异步流式
async def stream_example():
    messages = [{"role": "user", "content": "请写一个故事"}]
    async for chunk in client.async_stream_chat(messages):
        print(chunk, end="", flush=True)

asyncio.run(stream_example())
```

### 5. 便捷函数

```python
from utils.llm import chat, async_chat

# 同步便捷函数
response = chat("你好")
print(response)

# 异步便捷函数
async def main():
    response = await async_chat("请推荐几本书")
    print(response)

asyncio.run(main())
```

## API 参考

### DoubaoClient 类

#### 初始化参数

- `api_key`: API密钥（可选，默认从环境变量读取）
- `model`: 模型ID（默认：doubao-1-5-pro-32k-character-250715）

#### 主要方法

##### chat_completion(messages, **kwargs)
同步聊天完成

参数：
- `messages`: 消息列表
- `model`: 模型ID（可选）
- `temperature`: 温度参数（默认0.7）
- `max_tokens`: 最大token数（可选）
- `top_p`: top_p参数（默认1.0）
- `stream`: 是否流式响应（默认False）

##### async_chat_completion(messages, **kwargs)
异步聊天完成，参数同上

##### simple_chat(user_message, system_message=None)
简单同步聊天

##### async_simple_chat(user_message, system_message=None)
简单异步聊天

##### stream_chat(messages, **kwargs)
同步流式聊天

##### async_stream_chat(messages, **kwargs)
异步流式聊天

### 便捷函数

##### chat(user_message, system_message=None)
便捷的同步聊天函数

##### async_chat(user_message, system_message=None)
便捷的异步聊天函数

##### get_doubao_client(api_key=None, model=None)
获取豆包客户端实例（单例模式）

## 错误处理

```python
try:
    client = DoubaoClient(api_key="invalid_key")
    response = client.simple_chat("测试")
except Exception as e:
    print(f"调用失败: {e}")
```

## 环境变量

- `ARK_API_KEY`: 豆包API密钥

## 注意事项

1. 请确保API Key的安全性，不要在代码中硬编码
2. 流式响应适合长文本生成场景
3. 异步调用适合高并发场景
4. 建议在生产环境中使用环境变量管理API Key

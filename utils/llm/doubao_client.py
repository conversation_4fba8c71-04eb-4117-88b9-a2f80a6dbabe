"""
豆包大模型客户端封装
支持同步和异步调用
"""
import os
import time
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from volcenginesdkarkruntime import Ark, AsyncArk
import logging

if TYPE_CHECKING:
    from volcenginesdkarkruntime._streaming import AsyncStream

logger = logging.getLogger(__name__)


class DoubaoClient:
    """豆包大模型客户端"""

    def __init__(
            self,
            api_key: Optional[str] = None,
            model: str = "doubao-1-5-pro-32k-character-250715"
    ):
        """
        初始化豆包客户端

        Args:
            api_key: API密钥，如果不提供则从环境变量ARK_API_KEY读取
            model: 模型ID
        """
        self.api_key = api_key or os.environ.get("ARK_API_KEY")
        if not self.api_key:
            raise ValueError("API key is required. Please provide api_key or set ARK_API_KEY environment variable.")

        self.model = model

        # 初始化同步客户端
        self._sync_client = None
        # 初始化异步客户端
        self._async_client = None

    @property
    def sync_client(self) -> Ark:
        """获取同步客户端"""
        if self._sync_client is None:
            self._sync_client = Ark(api_key=self.api_key)
        return self._sync_client

    @property
    def async_client(self) -> AsyncArk:
        """获取异步客户端"""
        if self._async_client is None:
            self._async_client = AsyncArk(api_key=self.api_key)
        return self._async_client

    def chat_completion(
            self,
            messages: List[Dict[str, str]],
            model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        同步聊天完成

        Args:
            messages: 消息列表，格式：[{"role": "user", "content": "你好"}]
            model: 模型ID，如果不提供则使用初始化时的模型

        Returns:
            响应结果
        """
        try:
            completion = self.sync_client.chat.completions.create(
                model=model or self.model,
                messages=messages
            )

            return {
                "content": completion.choices[0].message.content,
                "role": completion.choices[0].message.role,
                "finish_reason": completion.choices[0].finish_reason,
                "usage": completion.usage.dict() if completion.usage else None,
                "model": completion.model
            }

        except Exception as e:
            logger.error(f"同步调用豆包API失败: {e}")
            raise

    async def async_chat_completion(
            self,
            messages: List[Dict[str, str]],
            model: Optional[str] = None
    ) -> "AsyncStream":
        """
        异步聊天完成（流式）

        Args:
            messages: 消息列表
            model: 模型ID，如果不提供则使用初始化时的模型

        Returns:
            异步流式响应
        """
        try:
            completion = await self.async_client.chat.completions.create(
                model=model or self.model,
                messages=messages,
                stream=True
            )

            return completion

        except Exception as e:
            logger.error(f"异步调用豆包API失败: {e}")
            raise

    def simple_chat(
            self,
            messages: List[Dict[str, str]],
            model: Optional[str] = None
    ) -> str:
        """
        简单聊天接口（同步）

        Args:
            messages: 上下文消息列表
            model: 模型ID（可选）

        Returns:
            AI回复内容
        """
        result = self.chat_completion(messages, model)
        return result["content"]

    async def async_simple_chat(
            self,
            messages: List[Dict[str, str]],
            model: Optional[str] = None
    ) -> str:
        """
        简单聊天接口（异步流式）

        Args:
            messages: 上下文消息列表
            model: 模型ID（可选）

        Returns:
            AI回复内容（完整内容）
        """
        t1 = time.time()
        stream = await self.async_chat_completion(messages, model)
        content = ""
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                content += chunk.choices[0].delta.content
        t2 = time.time()
        print(f"LLM耗时: {t2 - t1}")
        return content

    async def async_stream_chat(
            self,
            messages: List[Dict[str, str]],
            model: Optional[str] = None
    ):
        """
        流式聊天（异步）

        Args:
            messages: 上下文消息列表
            model: 模型ID（可选）

        Yields:
            流式响应内容
        """
        stream = await self.async_chat_completion(messages, model)
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    def close(self):
        """关闭客户端连接"""
        # 豆包SDK可能不需要显式关闭，但保留接口以备将来使用
        pass


# 创建默认实例
default_doubao_client = None


def get_doubao_client(
        api_key: Optional[str] = None,
        model: str = "doubao-1-5-pro-32k-character-250715"
) -> DoubaoClient:
    """
    获取豆包客户端实例（单例模式）
    
    Args:
        api_key: API密钥
        model: 模型ID
        
    Returns:
        DoubaoClient实例
    """
    global default_doubao_client
    if default_doubao_client is None:
        default_doubao_client = DoubaoClient(api_key=api_key, model=model)
    return default_doubao_client


# 便捷函数
def chat(messages: List[Dict[str, str]], model: Optional[str] = None) -> str:
    """
    便捷的同步聊天函数

    Args:
        messages: 上下文消息列表
        model: 模型ID（可选）

    Returns:
        AI回复内容
    """
    client = get_doubao_client()
    return client.simple_chat(messages, model)


async def async_chat(messages: List[Dict[str, str]], model: Optional[str] = None) -> str:
    """
    便捷的异步聊天函数

    Args:
        messages: 上下文消息列表
        model: 模型ID（可选）

    Returns:
        AI回复内容
    """
    client = get_doubao_client()
    return await client.async_simple_chat(messages, model)


# 辅助函数：构建消息列表
def build_messages(
        user_message: str,
        system_message: Optional[str] = None,
        history: Optional[List[Dict[str, str]]] = None
) -> List[Dict[str, str]]:
    """
    构建消息列表的辅助函数

    Args:
        user_message: 用户消息
        system_message: 系统消息（可选）
        history: 历史对话（可选）

    Returns:
        格式化的消息列表
    """
    messages = []

    # 添加系统消息
    if system_message:
        messages.append({"role": "system", "content": system_message})

    # 添加历史对话
    if history:
        messages.extend(history)

    # 添加当前用户消息
    messages.append({"role": "user", "content": user_message})

    return messages


# 更便捷的聊天函数（兼容旧版本）
def simple_chat(user_message: str, system_message: Optional[str] = None, model: Optional[str] = None) -> str:
    """
    简单聊天函数（兼容旧版本API）

    Args:
        user_message: 用户消息
        system_message: 系统消息（可选）
        model: 模型ID（可选）

    Returns:
        AI回复内容
    """
    messages = build_messages(user_message, system_message)
    return chat(messages, model)


async def async_simple_chat(user_message: str, system_message: Optional[str] = None,
                            model: Optional[str] = None) -> str:
    """
    简单异步聊天函数（兼容旧版本API）

    Args:
        user_message: 用户消息
        system_message: 系统消息（可选）
        model: 模型ID（可选）

    Returns:
        AI回复内容
    """
    messages = build_messages(user_message, system_message)
    return await async_chat(messages, model)

# 流式语音合成模块

## 📁 **文件结构**

```
utils/tts/
├── protocols/
│   ├── __init__.py
│   └── protocols.py          # WebSocket协议实现
├── bidirection.py            # 原始示例代码
├── streaming_tts.py          # 流式TTS核心类
├── bear_tts.py              # AI小熊专用TTS工具类
└── README.md                # 本文档
```

## 🎯 **模块概述**

本模块提供了字节跳动流式语音合成的完整封装，支持：
- 字符级流式合成
- 实时音频数据返回
- 多种声音类型
- 异步处理
- 错误处理和重连机制

## 📚 **API文档**

### **StreamingTTSClient 类**

#### 构造函数
```python
client = StreamingTTSClient(
    app_id="your_app_id",
    access_token="your_access_token",
    endpoint="wss://openspeech.bytedance.com/api/v3/tts/bidirection"
)
```

#### 主要方法

##### `connect(resource_id=None, voice_type="")`
建立WebSocket连接
```python
await client.connect(voice_type="zh_female_wanqudashu_moon_bigtts")
```

##### `synthesize_streaming(text, voice_type, ...)`
流式语音合成（异步生成器）
```python
async for audio_chunk in client.synthesize_streaming(
    text="你好世界",
    voice_type="zh_female_wanqudashu_moon_bigtts",
    encoding="mp3"
):
    # 处理音频块
    print(f"收到音频: {len(audio_chunk)} bytes")
```

##### `synthesize_complete(text, voice_type, ...)`
完整语音合成
```python
audio_data = await client.synthesize_complete(
    text="你好世界",
    voice_type="zh_female_wanqudashu_moon_bigtts"
)
```

##### `disconnect()`
断开连接
```python
await client.disconnect()
```

### **BearTTS 类（推荐使用）**

AI小熊专用的简化TTS接口

#### 构造函数
```python
tts = BearTTS(
    default_voice="zh_female_wanqudashu_moon_bigtts",
    default_encoding="mp3"
)
```

#### 主要方法

##### `speak(text, voice=None, encoding=None, streaming=False)`
语音合成
```python
# 普通合成
audio_data = await tts.speak("你好，我是AI小熊！")

# 流式合成
audio_data = await tts.speak("流式合成测试", streaming=True)
```

##### `speak_streaming(text, voice=None, encoding=None)`
流式语音合成（异步生成器）
```python
async for chunk in tts.speak_streaming("流式合成测试"):
    # 实时处理音频块
    play_audio_chunk(chunk)
```

## 🔧 **使用示例**

### **基础使用**

```python
import asyncio
from utils.tts.bear_tts import BearTTS

async def basic_example():
    # 使用上下文管理器（推荐）
    async with BearTTS() as tts:
        audio_data = await tts.speak("你好，我是AI小熊！")
        
        # 保存音频文件
        with open("output.mp3", "wb") as f:
            f.write(audio_data)
        
        print(f"音频合成完成，大小: {len(audio_data)} bytes")

asyncio.run(basic_example())
```

### **流式合成**

```python
async def streaming_example():
    async with BearTTS() as tts:
        audio_chunks = []
        
        # 定义音频回调函数
        def on_audio_chunk(chunk: bytes):
            print(f"实时收到音频: {len(chunk)} bytes")
            # 这里可以实时播放音频
        
        # 流式合成
        async for chunk in tts.speak_streaming(
            "这是一个流式合成的示例，每个字符都会实时生成音频。",
            audio_callback=on_audio_chunk
        ):
            audio_chunks.append(chunk)
        
        # 保存完整音频
        complete_audio = b''.join(audio_chunks)
        with open("streaming_output.mp3", "wb") as f:
            f.write(complete_audio)

asyncio.run(streaming_example())
```

### **便捷函数**

```python
from utils.tts.bear_tts import quick_speak, bear_speak

async def convenience_example():
    # 快速合成（一次性使用）
    audio1 = await quick_speak("快速合成测试")
    
    # AI小熊专用函数
    audio2 = await bear_speak("你好，我是AI小熊！", save_file="bear_output.mp3")
    
    print("便捷函数测试完成")

asyncio.run(convenience_example())
```

### **在AI小熊项目中集成**

```python
# 在你的AI小熊业务逻辑中
from utils.tts.bear_tts import get_tts_client

class BearClient:
    def __init__(self):
        self.tts = get_tts_client()
    
    async def start(self):
        await self.tts.connect()
    
    async def respond_to_user(self, text: str):
        """响应用户，生成语音"""
        try:
            # 生成语音
            audio_data = await self.tts.speak(text)
            
            # 发送给客户端
            await self.send_audio_to_client(audio_data)
            
        except Exception as e:
            logger.error(f"TTS failed: {e}")
    
    async def stream_response(self, text: str):
        """流式响应用户"""
        try:
            async for audio_chunk in self.tts.speak_streaming(text):
                # 实时发送音频块给客户端
                await self.send_audio_chunk_to_client(audio_chunk)
                
        except Exception as e:
            logger.error(f"Streaming TTS failed: {e}")
```

## 🎵 **支持的声音类型**

### **中文声音**
- `zh_female_wanqudashu_moon_bigtts` - 女声（温柔大叔）
- `zh_male_jingqiangdaxiaosheng_moon_bigtts` - 男声（京腔大小生）
- 更多声音类型请参考字节跳动官方文档

### **音频格式**
- `mp3` - MP3格式（推荐）
- `wav` - WAV格式
- `pcm` - PCM原始格式

### **采样率**
- `16000` - 16kHz（推荐）
- `24000` - 24kHz
- `48000` - 48kHz

## ⚙️ **配置说明**

### **环境配置**

在 `config.py` 中配置：
```python
# 字节跳动语音服务配置
AUDIO_APP_ID = "your_app_id"
AUDIO_ACCESS_TOKEN = "your_access_token"
```

### **参数说明**

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `text` | str | - | 要合成的文本 |
| `voice_type` | str | - | 声音类型 |
| `encoding` | str | "mp3" | 音频编码格式 |
| `sample_rate` | int | 16000 | 采样率 |
| `char_delay` | float | 0.005 | 字符间延迟（秒） |
| `enable_timestamp` | bool | True | 是否启用时间戳 |
| `disable_markdown_filter` | bool | False | 是否禁用markdown过滤 |

## 🔍 **错误处理**

### **常见错误**

#### 1. 连接失败
```python
try:
    await client.connect()
except Exception as e:
    logger.error(f"Connection failed: {e}")
    # 重试逻辑
```

#### 2. 合成失败
```python
try:
    audio_data = await tts.speak("测试文本")
except Exception as e:
    logger.error(f"Synthesis failed: {e}")
    # 降级处理
```

#### 3. 网络中断
```python
# 使用上下文管理器自动处理连接
async with BearTTS() as tts:
    # 自动连接和断开
    audio_data = await tts.speak("测试")
```

## 🚀 **性能优化**

### **连接复用**
```python
# 使用全局实例避免重复连接
tts = get_tts_client()
await tts.connect()

# 多次使用同一连接
audio1 = await tts.speak("第一句话")
audio2 = await tts.speak("第二句话")
```

### **流式处理**
```python
# 对于长文本，使用流式处理
async for chunk in tts.speak_streaming(long_text):
    # 实时处理，减少内存占用
    await process_audio_chunk(chunk)
```

### **异步并发**
```python
# 并发处理多个合成任务
tasks = [
    tts.speak("文本1"),
    tts.speak("文本2"),
    tts.speak("文本3")
]
results = await asyncio.gather(*tasks)
```

## 📝 **注意事项**

1. **连接管理**: 使用完毕后记得断开连接，或使用上下文管理器
2. **错误处理**: 网络异常时要有重试机制
3. **资源限制**: 注意API调用频率限制
4. **音频格式**: 根据客户端需求选择合适的音频格式
5. **文本长度**: 过长文本建议分段处理

## 🧪 **测试**

运行测试示例：
```bash
python examples/tts_streaming_example.py
```

测试包含：
- 基础合成功能
- 流式合成功能
- 多句子处理
- 不同声音类型
- 错误处理机制

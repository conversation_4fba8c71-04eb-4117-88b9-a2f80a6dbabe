#!/usr/bin/env python3
"""
AI小熊专用TTS工具类
基于新的流式TTS API，支持LLM流式输入
"""
import asyncio
import logging
from typing import Optional, Callable, Awaitable, Any, Coroutine
import config
from .streaming_tts import StreamingTTSClient

logger = logging.getLogger(__name__)


class BearTTS:
    """
    AI小熊TTS工具类
    支持LLM流式文本输入和实时音频输出
    """

    def __init__(
            self,
            app_id: Optional[str] = None,
            access_token: Optional[str] = None,
            voice_type: str = "zh_female_wanqudashu_moon_bigtts",
            encoding: str = "mp3",
            sample_rate: int = 16000
    ):
        """
        初始化TTS工具

        Args:
            app_id: 应用ID，默认从config读取
            access_token: 访问令牌，默认从config读取
            voice_type: 声音类型
            encoding: 音频编码格式
            sample_rate: 采样率
        """
        self.app_id = app_id or config.AUDIO_APP_ID
        self.access_token = access_token or config.AUDIO_ACCESS_TOKEN
        self.voice_type = voice_type
        self.encoding = encoding
        self.sample_rate = sample_rate

        self.client: Optional[StreamingTTSClient] = None
        self._connected = False

        if not self.app_id or not self.access_token:
            raise ValueError("app_id and access_token are required")

    async def connect(self) -> None:
        """建立连接"""
        print("tts尝试建立连接")
        if self._connected:
            print("tts已连接，不需要重复连接")
            return

        try:
            self.client = StreamingTTSClient(
                app_id=self.app_id,
                access_token=self.access_token,
                voice_type=self.voice_type,
                encoding=self.encoding,
                sample_rate=self.sample_rate
            )
            await self.client.connect()
            self._connected = True
            logger.info("TTS client connected successfully")
        except Exception as e:
            logger.error(f"Failed to connect TTS client: {e}")
            raise

    async def disconnect(self) -> None:
        """断开连接"""
        if self.client and self._connected:
            try:
                await self.client.disconnect()
                self._connected = False
                logger.info("TTS client disconnected")
            except Exception as e:
                logger.error(f"Error disconnecting TTS client: {e}")

    def set_audio_callback(self, callback: Callable | Coroutine) -> None:
        """
        设置音频回调函数

        Args:
            callback: 回调函数，参数为 (audio_data: bytes, is_last: bool)
        """
        if not self.client:
            raise RuntimeError("Client not initialized. Call connect() first.")

        self.client.set_callback(callback)
        logger.info("Audio callback set")

    async def async_text(self, text: str, is_last: bool = False) -> None:
        """
        异步输入文本（支持LLM流式输出）

        Args:
            text: 文本内容
            is_last: 是否为最后一段文本
        """
        if not self._connected:
            await self.connect()

        try:
            await self.client.async_text(text, is_last)
        except Exception as e:
            logger.error(f"TTS async_text failed: {e}")
            raise

    async def reset_session(self) -> None:
        """重置会话（用于新的对话）"""
        if self.client:
            await self.client.reset_session()

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected

    def is_session_active(self) -> bool:
        """检查会话是否活跃"""
        return self.client.is_session_active() if self.client else False

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()


# 全局TTS实例（单例模式）
_global_tts: Optional[BearTTS] = None


def get_tts_client(
        app_id: Optional[str] = None,
        access_token: Optional[str] = None,
        voice_type: str = "zh_female_wanqudashu_moon_bigtts"
) -> BearTTS:
    """
    获取全局TTS客户端实例（单例模式）

    Args:
        app_id: 应用ID
        access_token: 访问令牌
        voice_type: 声音类型

    Returns:
        BearTTS: TTS客户端实例
    """
    global _global_tts
    if _global_tts is None:
        _global_tts = BearTTS(app_id, access_token, voice_type)
    return _global_tts


# AI小熊专用便捷函数
async def bear_stream_tts(
        audio_callback: Callable[[bytes, bool], None],
        voice_type: str = "zh_female_wanqudashu_moon_bigtts"
) -> BearTTS:
    """
    创建AI小熊流式TTS实例

    Args:
        audio_callback: 音频回调函数 (audio_data: bytes, is_last: bool)
        voice_type: 声音类型

    Returns:
        BearTTS: 配置好的TTS实例
    """
    tts = BearTTS(voice_type=voice_type)
    await tts.connect()
    tts.set_audio_callback(audio_callback)
    return tts


# 使用示例
async def example_usage():
    """新API使用示例"""

    # 音频数据收集
    audio_chunks = []

    def audio_callback(audio_data: bytes, is_last: bool):
        if audio_data:
            audio_chunks.append(audio_data)
            print(f"收到音频: {len(audio_data)} bytes")

        if is_last:
            print("音频接收完成!")
            complete_audio = b''.join(audio_chunks)
            with open("bear_output.mp3", "wb") as f:
                f.write(complete_audio)
            print(f"音频已保存: {len(complete_audio)} bytes")

    # 方式1: 使用便捷函数
    tts = await bear_stream_tts(audio_callback)

    # 模拟LLM流式输出
    llm_outputs = ["你好，", "我是", "AI小熊！"]

    for i, text in enumerate(llm_outputs):
        is_last = (i == len(llm_outputs) - 1)
        await tts.async_text(text, is_last)
        await asyncio.sleep(0.5)

    await tts.disconnect()

    # 方式2: 使用上下文管理器
    async with BearTTS() as tts:
        tts.set_audio_callback(audio_callback)

        # 单次完整输入
        await tts.async_text("这是一次性输入的文本。", True)
        await asyncio.sleep(2)


# 集成到AI小熊项目的示例
class AIBearTTSManager:
    """AI小熊TTS管理器"""

    def __init__(self):
        self.tts: Optional[BearTTS] = None
        self.audio_buffer = bytearray()

    async def start(self, audio_callback: Optional[Callable[[bytes, bool], None]] = None):
        """启动TTS服务"""
        self.tts = BearTTS()
        await self.tts.connect()

        if audio_callback:
            self.tts.set_audio_callback(audio_callback)
        else:
            self.tts.set_audio_callback(self._default_audio_callback)

    def _default_audio_callback(self, audio_data: bytes, is_last: bool):
        """默认音频回调"""
        if audio_data:
            self.audio_buffer.extend(audio_data)
            # 这里可以实时发送给WebSocket客户端
            print(f"发送音频块: {len(audio_data)} bytes")

        if is_last:
            print(f"完整音频生成完成: {len(self.audio_buffer)} bytes")
            self.audio_buffer.clear()

    async def process_llm_stream(self, llm_text_stream):
        """处理LLM流式文本输出"""
        if not self.tts:
            raise RuntimeError("TTS not started")

        text_buffer = ""
        async for chunk in llm_text_stream:
            text_buffer += chunk

            # 遇到句号或缓冲区满时发送
            if "。" in chunk or len(text_buffer) > 20:
                await self.tts.async_text(text_buffer, False)
                text_buffer = ""

        # 发送剩余文本并标记结束
        if text_buffer:
            await self.tts.async_text(text_buffer, True)
        else:
            await self.tts.async_text("", True)

    async def stop(self):
        """停止TTS服务"""
        if self.tts:
            await self.tts.disconnect()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(example_usage())

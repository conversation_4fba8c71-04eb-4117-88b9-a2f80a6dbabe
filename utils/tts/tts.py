import asyncio
import json
import uuid

import websockets

from utils.tts.protocols.protocols import (
    EventType,
    MsgType,
    finish_connection,
    finish_session,
    receive_message,
    start_connection,
    start_session,
    task_request,
    wait_for_event,
)


class TTS:
    def __init__(self, app_id, access_token):
        self.app_id = app_id
        self.access_token = access_token
        self.api_url = "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
        self.ws: websockets.ClientConnection | None = None
        self.voice_type = "zh_male_xionger_mars_bigtts"
        self._session_id = None
        self.task = None

    async def start(self, text: str, callback, voice_type: str | None = None):
        if voice_type:
            self.voice_type = voice_type
        if self.task:
            self.task.cancel()
        await self._get_ws()
        await self._start_session()
        await self._sync_send(text)
        self.task = asyncio.create_task(self.async_recv(callback))

    async def async_recv(self, callback):
        while True:
            msg = await receive_message(self.ws)
            if msg.type == MsgType.FullServerResponse:
                if msg.event == EventType.SessionFinished:
                    await callback(b'', True)
                    await finish_connection(self.ws)
                    await self.ws.close()
                    break
            elif msg.type == MsgType.AudioOnlyServer:
                await callback(msg.payload, False)
            else:
                raise RuntimeError(f"TTS conversion failed: {msg}")

    async def _start_session(self):
        """
        初始化session配置
        """
        base_request = {
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "namespace": "BidirectionalTTS",
            "req_params": {
                "speaker": self.voice_type,
                "audio_params": {
                    "format": "mp3",
                    "sample_rate": 16000,
                    "enable_timestamp": True,
                },
                "additions": json.dumps(
                    {
                        "disable_markdown_filter": False,
                    }
                ),
            },
            "event": EventType.StartSession,
        }
        self._session_id = uuid.uuid4().hex
        await start_session(self.ws, json.dumps(base_request).encode(), self._session_id)
        await wait_for_event(self.ws, MsgType.FullServerResponse, EventType.SessionStarted)

    async def sync_tts(self, text: str, voice_type: str | None = None) -> bytes:
        """
        等待完整结果
        :param text: 完整文本
        """
        if voice_type:
            self.voice_type = voice_type
        await self._get_ws()
        await self._start_session()
        _, audio_bytes = await asyncio.gather(self._sync_send(text), self._sync_recv())
        await finish_connection(self.ws)
        await self.ws.close()
        return audio_bytes

    async def _sync_send(self, text: str):
        base_request = {
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "namespace": "BidirectionalTTS",
            "event": EventType.TaskRequest,
            "req_params": {
                "text": text,
            },
        }

        await task_request(
            self.ws, json.dumps(base_request).encode(), self._session_id
        )
        await finish_session(self.ws, self._session_id)

    async def _sync_recv(self) -> bytearray:
        audio_data = bytearray()
        while True:
            msg = await receive_message(self.ws)
            if msg.type == MsgType.FullServerResponse:
                if msg.event == EventType.SessionFinished:
                    break
            elif msg.type == MsgType.AudioOnlyServer:
                audio_data.extend(msg.payload)
            else:
                raise RuntimeError(f"TTS conversion failed: {msg}")
        return audio_data

    def _get_resource_id(self, voice_type: str) -> str:
        if voice_type.startswith("S_"):
            return "volc.megatts.default"
        return "volc.service_type.10029"

    async def _get_ws(self):
        headers = {
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.access_token,
            "X-Api-Resource-Id": self._get_resource_id(self.voice_type),
            "X-Api-Connect-Id": str(uuid.uuid4()),
        }
        self.ws = await websockets.connect(self.api_url, additional_headers=headers)
        await start_connection(self.ws)
        await wait_for_event(self.ws, MsgType.FullServerResponse, EventType.ConnectionStarted)


async def test_tts():
    text = "我是狡猾的皮球，是新仙二的开发者。新仙剑奇侠传二是一款免费游戏，请不要上当受骗了！"
    tts = TTS("1886250510", "xilBLR51DOuEEXZCZ0WnW3B2yybMF9pQ")
    audio_bytes = await tts.sync_tts(text)
    with open("test.mp3", "wb") as f:
        f.write(audio_bytes)


if __name__ == '__main__':
    asyncio.run(test_tts())

#!/usr/bin/env python3
"""
流式语音合成客户端
支持LLM流式文本输入，实时返回音频数据
"""
import asyncio
import copy
import json
import logging
import uuid
from typing import Callable, Optional, Dict, Any
import websockets

from .protocols.protocols import (
    EventType,
    MsgType,
    finish_connection,
    finish_session,
    receive_message,
    start_connection,
    start_session,
    task_request,
    wait_for_event,
)

logger = logging.getLogger(__name__)


class StreamingTTSClient:
    """
    流式语音合成客户端
    支持LLM流式文本输入，实时返回音频数据
    """

    def __init__(
        self,
        app_id: str,
        access_token: str,
        voice_type: str = "zh_female_wanqudashu_moon_bigtts",
        encoding: str = "mp3",
        sample_rate: int = 16000,
        endpoint: str = "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
    ):
        """
        初始化TTS客户端

        Args:
            app_id: 应用ID
            access_token: 访问令牌
            voice_type: 声音类型
            encoding: 音频编码格式
            sample_rate: 采样率
            endpoint: WebSocket端点URL
        """
        self.app_id = app_id
        self.access_token = access_token
        self.voice_type = voice_type
        self.encoding = encoding
        self.sample_rate = sample_rate
        self.endpoint = endpoint

        self.websocket: Optional[websockets.WebSocketClientProtocol] = None
        self.connect_id = str(uuid.uuid4())
        self._connected = False
        self._session_active = False
        self._session_id: Optional[str] = None

        # 回调函数
        self._audio_callback: Optional[Callable[[bytes, bool], None]] = None

        # 任务管理
        self._receive_task: Optional[asyncio.Task] = None
        self._text_buffer = ""
        
    def get_resource_id(self, voice: str) -> str:
        """根据声音类型获取资源ID"""
        if voice.startswith("S_"):
            return "volc.megatts.default"
        return "volc.service_type.10029"

    def set_callback(self, callback: Callable[[bytes, bool], None]) -> None:
        """
        设置音频回调函数

        Args:
            callback: 回调函数，参数为 (audio_data: bytes, is_last: bool)
        """
        self._audio_callback = callback
        logger.info("Audio callback set")

    async def connect(self) -> None:
        """建立WebSocket连接"""
        if self._connected:
            logger.warning("Already connected")
            return

        headers = {
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.access_token,
            "X-Api-Resource-Id": self.get_resource_id(self.voice_type),
            "X-Api-Connect-Id": self.connect_id,
        }

        logger.info(f"Connecting to {self.endpoint}")
        self.websocket = await websockets.connect(
            self.endpoint,
            additional_headers=headers,
            max_size=10 * 1024 * 1024
        )

        logger.info(f"Connected, Logid: {self.websocket.response.headers.get('x-tt-logid', 'N/A')}")

        # 启动连接
        await start_connection(self.websocket)
        await wait_for_event(
            self.websocket, MsgType.FullServerResponse, EventType.ConnectionStarted
        )

        self._connected = True
        logger.info("Connection established successfully")
    
    async def start_session(self) -> None:
        """启动TTS会话"""
        if not self._connected:
            raise RuntimeError("Not connected. Call connect() first.")

        if self._session_active:
            logger.warning("Session already active")
            return

        # 构建会话启动请求
        base_request = {
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "namespace": "BidirectionalTTS",
            "req_params": {
                "speaker": self.voice_type,
                "audio_params": {
                    "format": self.encoding,
                    "sample_rate": self.sample_rate,
                    "enable_timestamp": True,
                },
                "additions": json.dumps({
                    "disable_markdown_filter": False,
                }),
            },
        }

        # 启动会话
        self._session_id = str(uuid.uuid4())
        start_session_request = copy.deepcopy(base_request)
        start_session_request["event"] = EventType.StartSession

        await start_session(
            self.websocket,
            json.dumps(start_session_request).encode(),
            self._session_id
        )
        await wait_for_event(
            self.websocket, MsgType.FullServerResponse, EventType.SessionStarted
        )

        self._session_active = True

        # 启动音频接收任务
        self._receive_task = asyncio.create_task(self._receive_audio())

        logger.info(f"TTS session started: {self._session_id}")

    async def async_text(self, text: str, is_last: bool = False) -> None:
        """
        异步输入文本（支持LLM流式输出）

        Args:
            text: 文本内容
            is_last: 是否为最后一段文本
        """
        if not self._session_active:
            await self.start_session()

        # 将文本添加到缓冲区
        self._text_buffer += text

        # 构建合成请求
        base_request = {
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "namespace": "BidirectionalTTS",
            "req_params": {
                "speaker": self.voice_type,
                "audio_params": {
                    "format": self.encoding,
                    "sample_rate": self.sample_rate,
                    "enable_timestamp": True,
                },
                "additions": json.dumps({
                    "disable_markdown_filter": False,
                }),
            },
        }

        # 发送文本进行合成
        for char in text:
            synthesis_request = copy.deepcopy(base_request)
            synthesis_request["event"] = EventType.TaskRequest
            synthesis_request["req_params"]["text"] = char

            await task_request(
                self.websocket,
                json.dumps(synthesis_request).encode(),
                self._session_id
            )
            await asyncio.sleep(0.005)  # 5ms延迟

        # 如果是最后一段文本，结束会话
        if is_last:
            await self._finish_session()

        logger.info(f"Text sent: '{text}', is_last: {is_last}")

    async def _finish_session(self) -> None:
        """结束TTS会话"""
        if not self._session_active:
            return

        try:
            await finish_session(self.websocket, self._session_id)
            self._session_active = False

            # 等待接收任务完成
            if self._receive_task:
                await self._receive_task
                self._receive_task = None

            logger.info("TTS session finished")

        except Exception as e:
            logger.error(f"Error finishing session: {e}")

    async def _receive_audio(self) -> None:
        """接收音频数据的后台任务"""
        try:
            while self._session_active:
                msg = await receive_message(self.websocket)

                if msg.type == MsgType.FullServerResponse:
                    if msg.event == EventType.SessionFinished:
                        # 会话结束，发送最后的回调
                        if self._audio_callback:
                            self._audio_callback(b'', True)
                        break
                elif msg.type == MsgType.AudioOnlyServer:
                    if msg.payload and self._audio_callback:
                        # 发送音频数据回调
                        self._audio_callback(msg.payload, False)
                else:
                    logger.warning(f"Unexpected message: {msg}")

        except Exception as e:
            logger.error(f"Error receiving audio: {e}")
        finally:
            self._session_active = False

    async def disconnect(self) -> None:
        """断开连接"""
        if not self._connected or not self.websocket:
            return

        try:
            # 如果会话还在进行，先结束会话
            if self._session_active:
                await self._finish_session()

            # 结束连接
            await finish_connection(self.websocket)
            await wait_for_event(
                self.websocket, MsgType.FullServerResponse, EventType.ConnectionFinished
            )
            await self.websocket.close()
            logger.info("Connection closed")

        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
        finally:
            self._connected = False
            self._session_active = False
            self.websocket = None
            if self._receive_task:
                self._receive_task.cancel()
                self._receive_task = None

    async def reset_session(self) -> None:
        """重置会话（用于新的对话）"""
        if self._session_active:
            await self._finish_session()

        self._text_buffer = ""
        logger.info("Session reset")

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected

    def is_session_active(self) -> bool:
        """检查会话是否活跃"""
        return self._session_active

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()

    def __del__(self):
        """析构函数"""
        if self._connected and self.websocket:
            logger.warning("TTS client was not properly closed")
    
# 使用示例
async def example_usage():
    """使用示例：模拟LLM流式输出"""
    import config

    # 创建TTS客户端
    tts_client = StreamingTTSClient(
        app_id=config.AUDIO_APP_ID,
        access_token=config.AUDIO_ACCESS_TOKEN,
        voice_type="zh_female_wanqudashu_moon_bigtts"
    )

    # 音频数据收集
    audio_chunks = []

    # 设置音频回调函数
    def audio_callback(audio_data: bytes, is_last: bool):
        if audio_data:
            audio_chunks.append(audio_data)
            print(f"收到音频块: {len(audio_data)} bytes")

        if is_last:
            print("音频接收完成!")
            # 保存完整音频
            complete_audio = b''.join(audio_chunks)
            with open("llm_tts_output.mp3", "wb") as f:
                f.write(complete_audio)
            print(f"音频已保存，总大小: {len(complete_audio)} bytes")

    try:
        # 连接TTS服务
        await tts_client.connect()
        tts_client.set_callback(audio_callback)

        # 模拟LLM流式输出文本
        llm_outputs = [
            "你好，",
            "我是",
            "AI小熊，",
            "很高兴",
            "为您",
            "服务！"
        ]

        print("开始模拟LLM流式输出...")
        for i, text_chunk in enumerate(llm_outputs):
            is_last = (i == len(llm_outputs) - 1)
            print(f"LLM输出: '{text_chunk}' (is_last: {is_last})")

            # 发送文本到TTS
            await tts_client.async_text(text_chunk, is_last)

            # 模拟LLM输出间隔
            await asyncio.sleep(0.5)

        # 等待音频处理完成
        await asyncio.sleep(2)

    finally:
        await tts_client.disconnect()


async def example_chat_integration():
    """聊天集成示例"""
    import config

    class ChatBot:
        def __init__(self):
            self.tts = StreamingTTSClient(
                app_id=config.AUDIO_APP_ID,
                access_token=config.AUDIO_ACCESS_TOKEN
            )
            self.audio_buffer = bytearray()

        async def start(self):
            await self.tts.connect()
            self.tts.set_callback(self._on_audio_received)

        def _on_audio_received(self, audio_data: bytes, is_last: bool):
            """音频接收回调"""
            if audio_data:
                self.audio_buffer.extend(audio_data)
                # 实时发送给客户端
                self._send_to_client(audio_data)

            if is_last:
                print(f"完整回复音频生成完成，总大小: {len(self.audio_buffer)} bytes")
                self.audio_buffer.clear()

        def _send_to_client(self, audio_chunk: bytes):
            """发送音频块给客户端（模拟）"""
            print(f"→ 发送音频块给客户端: {len(audio_chunk)} bytes")

        async def process_llm_response(self, llm_stream):
            """处理LLM流式响应"""
            text_buffer = ""

            async for chunk in llm_stream:
                text_buffer += chunk

                # 当遇到句号或达到一定长度时发送
                if "。" in chunk or len(text_buffer) > 10:
                    await self.tts.async_text(text_buffer, False)
                    text_buffer = ""

            # 发送剩余文本并标记结束
            if text_buffer:
                await self.tts.async_text(text_buffer, True)
            else:
                await self.tts.async_text("", True)  # 标记结束

        async def stop(self):
            await self.tts.disconnect()

    # 使用示例
    bot = ChatBot()
    await bot.start()

    # 模拟LLM流式响应
    async def mock_llm_stream():
        responses = ["今天", "天气", "很好，", "适合", "出门", "散步。"]
        for resp in responses:
            yield resp
            await asyncio.sleep(0.3)

    await bot.process_llm_response(mock_llm_stream())
    await bot.stop()


if __name__ == "__main__":
    print("=== TTS流式文本输入示例 ===")
    asyncio.run(example_usage())

    print("\n=== 聊天集成示例 ===")
    asyncio.run(example_chat_integration())



"""
字节跳动ASR客户端封装
支持输入MP3 bytes，输出ASR文本
"""
import json
import time
import uuid
import requests
import base64
import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class AsrClient:
    """字节跳动ASR客户端"""

    def __init__(
            self,
            app_key: Optional[str] = None,
            access_key: Optional[str] = None
    ):
        """
        初始化ASR客户端

        Args:
            app_key: 应用密钥，如果不提供则从环境变量读取
            access_key: 访问密钥，如果不提供则从环境变量读取
        """
        self.app_key = app_key
        self.access_key = access_key

        if not self.app_key or not self.access_key:
            raise ValueError("app_key and access_key are required. Please provide them or set environment variables.")

        self.recognize_url = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/recognize/flash"

    def recognize_from_bytes(self, audio_bytes: bytes) -> str:
        """
        从音频字节数据识别文本

        Args:
            audio_bytes: MP3音频字节数据

        Returns:
            识别出的文本字符串
        """
        try:
            # 将音频字节转换为base64
            base64_data = base64.b64encode(audio_bytes).decode('utf-8')

            # 构建请求头
            headers = {
                "X-Api-App-Key": self.app_key,
                "X-Api-Access-Key": self.access_key,
                "X-Api-Resource-Id": "volc.bigasr.auc_turbo",
                "X-Api-Request-Id": uuid.uuid4().hex,
                "X-Api-Sequence": "-1",
            }

            # 构建请求体
            request_data = {
                "user": {
                    "uid": self.app_key
                },
                "audio": {
                    "data": base64_data
                },
                "request": {
                    "model_name": "bigmodel",
                    "enable_itn": False,
                    "enable_punc": False,
                    "enable_ddc": False
                }
            }

            t1 = time.time()
            # 发送请求
            response = requests.post(self.recognize_url, json=request_data, headers=headers)

            # 检查响应状态
            if 'X-Api-Status-Code' not in response.headers:
                raise RuntimeError(f"ASR request failed: {response.headers}")

            status_code = response.headers['X-Api-Status-Code']
            if status_code != '20000000':
                error_msg = response.headers.get('X-Api-Message', 'Unknown error')
                raise RuntimeError(f"ASR failed with code {status_code}: {error_msg}")
            t2 = time.time()
            print("耗时：",t2-t1)
            # 解析响应结果
            result = response.json()

            print(result)
        except Exception as e:
            logger.error(f"ASR recognition failed: {e}")
            raise

    def recognize_from_file(self, file_path: str) -> str:
        """
        从文件路径识别文本

        Args:
            file_path: 音频文件路径

        Returns:
            识别出的文本字符串
        """
        try:
            with open(file_path, 'rb') as f:
                audio_bytes = f.read()
            return self.recognize_from_bytes(audio_bytes)
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise


# 创建默认实例
default_asr_client = None


def get_asr_client(
        app_key: Optional[str] = None,
        access_key: Optional[str] = None
) -> AsrClient:
    """
    获取ASR客户端实例（单例模式）

    Args:
        app_key: 应用密钥
        access_key: 访问密钥

    Returns:
        AsrClient实例
    """
    global default_asr_client
    if default_asr_client is None:
        default_asr_client = AsrClient(app_key=app_key, access_key=access_key)
    return default_asr_client

import asyncio
import gzip
import json
import struct
import traceback
import uuid
from typing import Dict, Any, Callable, Awaitable

import websockets


class ProtocolVersion:
    V1 = 0b0001


class MessageType:
    CLIENT_FULL_REQUEST = 0b0001
    CLIENT_AUDIO_ONLY_REQUEST = 0b0010
    SERVER_FULL_RESPONSE = 0b1001
    SERVER_ERROR_RESPONSE = 0b1111


class MessageTypeSpecificFlags:
    NO_SEQUENCE = 0b0000
    POS_SEQUENCE = 0b0001
    NEG_SEQUENCE = 0b0010
    NEG_WITH_SEQUENCE = 0b0011


class SerializationType:
    NO_SERIALIZATION = 0b0000
    JSON = 0b0001


class CompressionType:
    GZIP = 0b0001


class AsrRequestHeader:
    def __init__(self):
        self.message_type = MessageType.CLIENT_FULL_REQUEST
        self.message_type_specific_flags = MessageTypeSpecificFlags.POS_SEQUENCE
        self.serialization_type = SerializationType.JSON
        self.compression_type = CompressionType.GZIP
        self.reserved_data = bytes([0x00])

    def with_message_type(self, message_type: int) -> 'AsrRequestHeader':
        self.message_type = message_type
        return self

    def with_message_type_specific_flags(self, flags: int) -> 'AsrRequestHeader':
        self.message_type_specific_flags = flags
        return self

    def with_serialization_type(self, serialization_type: int) -> 'AsrRequestHeader':
        self.serialization_type = serialization_type
        return self

    def with_compression_type(self, compression_type: int) -> 'AsrRequestHeader':
        self.compression_type = compression_type
        return self

    def with_reserved_data(self, reserved_data: bytes) -> 'AsrRequestHeader':
        self.reserved_data = reserved_data
        return self

    def to_bytes(self) -> bytes:
        header = bytearray()
        header.append((ProtocolVersion.V1 << 4) | 1)
        header.append((self.message_type << 4) | self.message_type_specific_flags)
        header.append((self.serialization_type << 4) | self.compression_type)
        header.extend(self.reserved_data)
        return bytes(header)

    @staticmethod
    def default_header() -> 'AsrRequestHeader':
        return AsrRequestHeader()


class LLMAsrClient:
    """
    字节跳动ASR客户端封装
    """

    def __init__(self, app_id, access_token):
        self.app_id = app_id
        self.access_token = access_token
        self.api_url = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_async"
        self.ws: websockets.ClientConnection | None = None
        self._seq = 1
        self.recv_task = None
        self.callback: Callable[[str, bool], Awaitable[Any]] | None = None

    async def start(self, callback: Callable[[str, bool], Awaitable[Any]]):
        """
        只有在start之后，才可以进行后续的发送音频字节流
        """
        await self._init(callback)
        # 云端并发数不够，不可复用连接
        # if not self.ws or self.ws.state != websockets.protocol.State.OPEN:
        #     await self._get_ws()
        # else:
        #     print("复用连接")
        self.recv_task = asyncio.create_task(self.recv())

    async def _init(self, callback: Callable[[str, bool], Awaitable[Any]] | None):
        self._seq = 1
        self.callback = callback
        if self.ws:
            await self.ws.close()
        if self.recv_task:
            self.recv_task.cancel()
        await self._get_ws()

    async def recv(self):
        """
        接收服务端返回的数据
        """
        if self.ws is None:
            raise Exception("请先调用start方法")

        while True:
            data = await self.ws.recv()
            try:
                response = ResponseParser.parse_response(data)
                resp = response.to_dict()
                print("asr返回：", resp)
                text = resp.get("payload_msg", {}).get("result", {}).get("text")
                is_last = resp.get("is_last_package")
                if text is not None:
                    await self.callback(text, is_last)
                if is_last:
                    break
            except:
                traceback.print_exc()
                break
        print("退出循环ASR")

    async def sync_recognize(self, audio_bytes: bytes) -> str:
        """
        同步识别
        """
        await self._init(None)
        send_result, recv_result = await asyncio.gather(self._sync_send(audio_bytes), self._sync_recv())
        return recv_result

    async def _sync_send(self, audio_bytes: bytes):
        """
        循环发送音频，每次发送6656字节
        """
        chunk_size = 6656
        for offset in range(0, len(audio_bytes), chunk_size):
            await self.send_audio(audio_bytes[offset:offset + chunk_size])
            await asyncio.sleep(0.2)
            # print("发送chunk")
        await self.send_audio(bytes([]), True)

    async def _sync_recv(self) -> str:
        while True:
            data = await self.ws.recv()
            try:
                response = ResponseParser.parse_response(data)
                resp = response.to_dict()
                text = resp.get("payload_msg", {}).get("result", {}).get("text")
                is_last = resp.get("is_last_package")
                # print(text)
                if is_last:
                    return text
            except:
                traceback.print_exc()
                break
        return ""

    async def send_audio(self, audio_bytes: bytes, is_last=False):
        """
        发送音频字节流
        """
        if self.ws is None:
            raise Exception("请先调用start方法")
        pck = self._build_audio_package(self._seq, audio_bytes, is_last)
        await self._send(pck)

    async def _send(self, pck: bytes):
        await self.ws.send(pck)
        self._seq += 1

    @staticmethod
    def _build_audio_package(seq: int, audio_bytes: bytes, is_last=False) -> bytes:
        header = AsrRequestHeader.default_header()
        if is_last:  # 最后一个包特殊处理
            header.with_message_type_specific_flags(MessageTypeSpecificFlags.NEG_WITH_SEQUENCE)
            seq = -seq  # 设为负值
        else:
            header.with_message_type_specific_flags(MessageTypeSpecificFlags.POS_SEQUENCE)
        header.with_message_type(MessageType.CLIENT_AUDIO_ONLY_REQUEST)

        request = bytearray()
        request.extend(header.to_bytes())
        request.extend(struct.pack('>i', seq))

        compressed_segment = gzip.compress(audio_bytes)
        request.extend(struct.pack('>I', len(compressed_segment)))
        request.extend(compressed_segment)
        return bytes(request)

    async def _get_ws(self):
        """
        与服务端建立连接并返回连接成功的ws
        """
        # 发起握手请求
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE  # 禁用验证

        ws = await websockets.connect(self.api_url, additional_headers={
            "X-Api-App-Key": self.app_id,
            "X-Api-Access-Key": self.access_token,
            "X-Api-Resource-Id": "volc.bigasr.sauc.duration",
            "X-Api-Request-Id": uuid.uuid4().hex
        },ssl=ssl_context)
        self.ws = ws

        # 发送初始化包（配置本次业务参数）
        init_pck = self._init_package(self._seq)
        await self._send(init_pck)
        return ws

    @staticmethod
    def _init_package(seq):
        """
        构造初始化数据包（按照字节的说法，叫做构造客户端完整请求）
        """
        header = AsrRequestHeader.default_header().with_message_type_specific_flags(
            MessageTypeSpecificFlags.POS_SEQUENCE
        )

        payload = {
            "user": {
                "uid": "demo_uid"
            },
            "audio": {
                "format": "pcm",
                "codec": "raw",
                "rate": 16000,
                "bits": 16,
                "channel": 1
            },
            "request": {
                "model_name": "bigmodel",
                "enable_itn": True,
                "enable_punc": True,
                "enable_ddc": True,
                "show_utterances": False,
                "enable_nonstream": False
            }
        }

        payload_bytes = json.dumps(payload).encode('utf-8')
        compressed_payload = gzip.compress(payload_bytes)
        payload_size = len(compressed_payload)

        request = bytearray()
        request.extend(header.to_bytes())
        request.extend(struct.pack('>i', seq))  # 使用传入的seq
        request.extend(struct.pack('>I', payload_size))
        request.extend(compressed_payload)

        return bytes(request)


class AsrResponse:
    def __init__(self):
        self.code = 0
        self.event = 0
        self.is_last_package = False
        self.payload_sequence = 0
        self.payload_size = 0
        self.payload_msg = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "code": self.code,
            "event": self.event,
            "is_last_package": self.is_last_package,
            "payload_sequence": self.payload_sequence,
            "payload_size": self.payload_size,
            "payload_msg": self.payload_msg
        }


class ResponseParser:
    @staticmethod
    def parse_response(msg: bytes) -> AsrResponse:
        response = AsrResponse()

        header_size = msg[0] & 0x0f
        message_type = msg[1] >> 4
        message_type_specific_flags = msg[1] & 0x0f
        serialization_method = msg[2] >> 4
        message_compression = msg[2] & 0x0f

        payload = msg[header_size * 4:]

        # 解析message_type_specific_flags
        if message_type_specific_flags & 0x01:
            response.payload_sequence = struct.unpack('>i', payload[:4])[0]
            payload = payload[4:]
        if message_type_specific_flags & 0x02:
            response.is_last_package = True
        if message_type_specific_flags & 0x04:
            response.event = struct.unpack('>i', payload[:4])[0]
            payload = payload[4:]

        # 解析message_type
        if message_type == MessageType.SERVER_FULL_RESPONSE:
            response.payload_size = struct.unpack('>I', payload[:4])[0]
            payload = payload[4:]
        elif message_type == MessageType.SERVER_ERROR_RESPONSE:
            response.code = struct.unpack('>i', payload[:4])[0]
            response.payload_size = struct.unpack('>I', payload[4:8])[0]
            payload = payload[8:]

        if not payload:
            return response

        # 解压缩
        if message_compression == CompressionType.GZIP:
            try:
                payload = gzip.compress(payload)
            except Exception as e:
                print(f"Failed to decompress payload: {e}")
                return response

        # 解析payload
        try:
            if serialization_method == SerializationType.JSON:
                response.payload_msg = json.loads(payload.decode('utf-8'))
        except Exception as e:
            print(f"Failed to parse payload: {e}")

        return response


async def cb(text, is_last):
    print(is_last, text)


async def main():
    client = LLMAsrClient("1886250510", "xilBLR51DOuEEXZCZ0WnW3B2yybMF9pQ")
    await client.start(cb)
    with open(r"D:\mydir\ai_bear\1.pcm", mode='rb') as file:
        chunk_size = 6656
        while True:
            data = file.read(chunk_size)
            if not data:
                break
            await client.send_audio(data)
            await asyncio.sleep(0.2)
        await client.send_audio(bytes([]), True)

    await asyncio.sleep(20)


async def test_sync_reg():
    client = LLMAsrClient("1886250510", "xilBLR51DOuEEXZCZ0WnW3B2yybMF9pQ")
    with open(r"D:\mydir\ai_bear\1.pcm", mode='rb') as file:
        data = file.read()
        text = await client.sync_recognize(data)
        print(text)


if __name__ == '__main__':
    asyncio.run(main())
    a = {
        'code': 0,
        'event': 0,
        'is_last_package': False,
        'payload_sequence': 4,
        'payload_size': 137,
        'payload_msg': {
            'audio_info': {'duration': 2496},
            'result': {
                'additions': {'log_id': '20250727203449F6EE6FAC033D06F0A573'},
                'text': '这是一段测试音频'
            }
        }
    }


from volcenginesdkarkruntime import Ark
# 从环境变量中读取您的方舟API Key
client = Ark(api_key="1a3f154a-1278-477b-8c17-ecfea7883443")
completion = client.chat.completions.create(
    # 替换 <Model>为 Model ID
    model="doubao-1-5-pro-32k-character-250715",
    messages=[
        {"role": "user", "content": "你好"}
    ]
)
print(completion.choices[0].message)


import asyncio
import os
from volcenginesdkarkruntime import AsyncArk
# 从环境变量中获取您的API KEY，配置方法见：https://www.volcengine.com/docs/82379/1399008
client = AsyncArk(
    api_key=os.environ.get("ARK_API_KEY"),
)

async def main() -> None:
    stream = await client.chat.completions.create(
        # 替换 <MODEL> 为模型的Model ID , 查询Model ID：https://www.volcengine.com/docs/82379/1330310
        model="<MODEL>",
        messages=[
            {"role": "system", "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手"},
            {"role": "user", "content": "常见的十字花科植物有哪些？"},
        ],
        stream=True
    )
    async for completion in stream:
        print(completion.choices[0].delta.content, end="")
    print()

asyncio.run(main())
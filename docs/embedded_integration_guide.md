# AI小熊嵌入式端对接文档

## 1. 通信协议介绍

### 连接方式
- **协议**: WebSocket
- **地址**: `ws://服务器IP:端口/main`
- **消息类型**:
  - 文本消息: 控制指令
  - 二进制消息: 音频数据

## 2. 支持的CMD

### 语音交互指令
- `ClientStartCmd null` - 开始接收音频流
- `ClientStopCmd null` - 停止接收音频流
- 音频数据流 - PCM 16kHz 16bit 单声道二进制数据

### 压力传感器指令
- `PressCmd 部位||压力值`
- 部位: face, neck, belly, hand 等
- 压力值: 1(轻压), 2(中压), 3(重压)
- 示例: `PressCmd face||2`

## 3. 交互时序图

### 语音交互时序
```
嵌入式设备              服务器
    |                     |
    |-- ClientStartCmd -->|
    |-- PCM音频数据 ----->|
    |-- PCM音频数据 ----->|
    |-- ClientStopCmd --->|
    |<---- MP3音频数据 ---|
    |<---- MP3音频数据 ---|
```

### 压力传感器时序
```
嵌入式设备              服务器
    |                     |
    |-- PressCmd face||2->|
    |<---- MP3音频数据 ---|
```

## 4. 对接步骤

### 第一步：建立WebSocket连接
1. 连接到服务器的WebSocket端点 `/main`
2. 处理连接成功/失败事件
3. 设置消息接收回调函数

### 第二步：实现压力传感器功能
1. 读取压力传感器数值（模拟量或数字量）
2. 将传感器数值映射为压力等级 1-3
3. 发送 `PressCmd 部位||压力值` 文本消息
4. 接收服务器返回的MP3音频数据并播放

### 第三步：实现语音交互功能
1. **音频采集**：
   - 配置麦克风采集 16kHz 16bit 单声道音频
   - 实现音量检测算法（如RMS计算）

2. **语音活动检测**：
   - 当音量超过阈值时，发送 `ClientStartCmd null`
   - 开始持续发送PCM音频数据（二进制消息）
   - 当音量低于阈值且持续2-3秒时，发送 `ClientStopCmd null`

3. **音频播放**：
   - 接收服务器返回的MP3音频数据
   - 解码MP3并通过扬声器播放

### 第四步：集成测试
1. 测试WebSocket连接稳定性
2. 测试压力传感器响应和音频播放
3. 测试语音交互的完整流程
4. 优化音频质量和响应延迟

## 5. 技术要点

### 音频格式要求
- **输入音频**: PCM 16kHz 16bit 单声道
- **输出音频**: MP3格式（需要解码播放）
- **建议发送频率**: 每200ms发送一次音频数据

### 压力传感器处理
- 传感器直接提供压力值，无需时间计算
- 根据实际传感器特性映射到1-3的压力等级
- 可支持多个部位的传感器

### 语音活动检测
- 实现音量阈值检测
- 避免频繁开始/停止录音
- 处理环境噪声干扰

### 错误处理
- WebSocket连接断开重连
- 音频采集/播放异常处理
- 传感器读取异常处理

# AI小熊嵌入式端对接文档

## 1. 通信协议介绍

### 连接方式
- **协议**: WebSocket
- **地址**: `ws://服务器IP:端口/main`
- **消息类型**:
  - 文本消息: 控制指令
  - 二进制消息: 音频数据

## 2. 支持的CMD

### 语音交互指令
- `ClientStartCmd null` - 开始接收音频流
- `ClientStopCmd null` - 停止接收音频流
- 音频数据流 - PCM 16kHz 16bit 单声道二进制数据

### 压力传感器指令
- `PressCmd 部位||压力值`
- 部位: face, neck, belly, hand 等
- 压力值: 1(轻压), 2(中压), 3(重压)
- 示例: `PressCmd face||2`

## 3. 交互流程图

### 语音交互流程
```
开始
  ↓
检测音量
  ↓
音量 > 阈值？ ──否──→ 继续检测
  ↓是
发送 ClientStartCmd
  ↓
持续发送PCM音频数据
  ↓
检测音量
  ↓
音量 < 阈值 且 持续2-3秒？ ──否──→ 继续发送音频
  ↓是
发送 ClientStopCmd
  ↓
接收MP3音频数据
  ↓
播放音频
  ↓
结束
```

### 压力传感器流程
```
开始
  ↓
读取传感器数值
  ↓
有压力变化？ ──否──→ 继续检测
  ↓是
映射压力等级(1-3)
  ↓
发送 PressCmd 部位||压力值
  ↓
接收MP3音频数据
  ↓
播放音频
  ↓
结束
```

## 4. 对接步骤

### 第一步：建立WebSocket连接
1. 连接到服务器的WebSocket端点 `/main`
2. 处理连接成功/失败事件
3. 设置消息接收回调函数

### 第二步：实现压力传感器功能
1. 读取压力传感器数值（模拟量或数字量）
2. 将传感器数值映射为压力等级 1-3
3. 发送 `PressCmd 部位||压力值` 文本消息
4. 接收服务器返回的MP3音频数据并播放

### 第三步：实现语音交互功能
1. **音频采集**：
   - 配置麦克风采集 16kHz 16bit 单声道音频

2. **语音活动检测**：
   - 当音量超过阈值或被唤醒词唤醒时，发送 `ClientStartCmd null`
   - 开始持续发送PCM音频数据（二进制消息）
   - 当一段时间没人说话时（一般两三秒，视情况而定），发送 `ClientStopCmd null`

3. **音频播放**：
   - 接收服务器返回的MP3音频数据
   - 解码MP3并通过扬声器播放

### 第四步：集成测试
1. 测试WebSocket连接稳定性
2. 测试压力传感器响应和音频播放
3. 测试语音交互的完整流程
4. 优化音频质量和响应延迟

## 5. 技术要点

### 音频格式要求
- **输入音频**: PCM 16kHz 16bit 单声道
- **输出音频**: MP3格式（需要解码播放）
- **建议发送频率**: 每200ms发送一次音频数据
- 接收音频时注意缓冲，不然会卡顿

### 压力传感器处理
- 传感器直接提供压力值，无需时间计算
- 根据实际传感器特性映射到1-3的压力等级
- 可支持多个部位的传感器


## 伪代码仅供参考
```
// 语音交互主流程
function voiceInteractionLoop() {
    初始化音频采集设备()
    初始化WebSocket连接()
    
    isRecording = false
    silenceStartTime = 0
    VOICE_THRESHOLD = 设定的音量阈值
    SILENCE_DURATION = 2000  // 2秒静音判断
    
    while (true) {
        // 采集音频数据
        audioData = 采集音频数据(200ms)  // 每200ms采集一次
        
        // 计算音量
        volume = calculateRMS(audioData)
        
        if (volume > VOICE_THRESHOLD) {
            // 检测到声音
            if (!isRecording) {
                // 开始录音
                websocket.send("ClientStartCmd null")
                isRecording = true
                print("开始录音")
            }
            
            // 发送音频数据
            websocket.sendBinary(audioData)
            
            // 重置静音计时
            silenceStartTime = 0
            
        } else {
            // 检测到静音
            if (isRecording) {
                if (silenceStartTime == 0) {
                    // 开始计时静音时长
                    silenceStartTime = getCurrentTime()
                } else {
                    // 检查静音时长
                    silenceDuration = getCurrentTime() - silenceStartTime
                    if (silenceDuration >= SILENCE_DURATION) {
                        // 静音超过阈值，停止录音
                        websocket.send("ClientStopCmd null")
                        isRecording = false
                        silenceStartTime = 0
                        print("停止录音")
                    }
                }
            }
        }
        
        delay(200)  // 200ms间隔
    }
}

// 音量计算函数
function calculateRMS(audioData) {
    sum = 0
    for (sample in audioData) {
        sum += sample * sample
    }
    return sqrt(sum / audioData.length)
}

// WebSocket消息接收处理
function onWebSocketMessage(message) {
    if (message.type == BINARY) {
        // 接收到MP3音频数据
        playAudio(message.data)
    } else {
        // 接收到文本消息
        print("收到服务器消息: " + message.text)
    }
}

// 音频播放函数
function playAudio(mp3Data) {
    // 解码MP3数据
    pcmData = decodeMp3(mp3Data)
    
    // 播放PCM音频
    audioOutput.play(pcmData)
}

// 主程序入口
function main() {
    // 连接WebSocket
    websocket.connect("ws://服务器IP:端口/main")
    websocket.onMessage = onWebSocketMessage
    
    // 启动语音交互循环
    voiceInteractionLoop()
}
```
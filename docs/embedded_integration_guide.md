# AI小熊嵌入式端对接文档

## 1. 通信协议介绍

### WebSocket连接
- **连接地址**: `ws://服务器IP:端口/main`
- **协议**: WebSocket
- **消息类型**:
  - 文本消息: 控制指令
  - 二进制消息: 音频数据

### ESP32连接示例
```c
#include <WiFi.h>
#include <WebSocketsClient.h>

WebSocketsClient webSocket;

void setup() {
    WiFi.begin("SSID", "PASSWORD");
    webSocket.begin("*************", 8000, "/main");
    webSocket.onEvent(webSocketEvent);
}

void webSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
    switch(type) {
        case WStype_CONNECTED:
            Serial.println("WebSocket Connected");
            break;
        case WStype_BIN:
            // 接收到音频数据
            playAudio(payload, length);
            break;
    }
}
```

## 2. 支持的CMD

### 2.1 语音交互指令

#### ClientStartCmd
```
ClientStartCmd null
```
- **类型**: 文本消息
- **用途**: 通知服务器开始接收音频流
- **发送时机**: 检测到用户开始说话

#### ClientStopCmd
```
ClientStopCmd null
```
- **类型**: 文本消息
- **用途**: 通知服务器停止接收音频流
- **发送时机**: 检测到用户停止说话

#### 音频数据流
- **类型**: 二进制消息
- **格式**: PCM 16kHz 16bit 单声道
- **发送**: 在ClientStartCmd和ClientStopCmd之间持续发送

### 2.2 压力传感器指令

#### PressCmd
```
PressCmd 部位||压力值
```
- **类型**: 文本消息
- **参数**:
  - 部位: face, neck, belly, hand 等
  - 压力值: 1(轻压), 2(中压), 3(重压)
- **示例**: `PressCmd face||2`

## 3. 交互时序图

### 3.1 语音交互时序
```
ESP32                    服务器
  |                        |
  |-- ClientStartCmd ----->|
  |                        |
  |-- PCM音频数据 -------->|
  |-- PCM音频数据 -------->|
  |-- PCM音频数据 -------->|
  |                        |
  |-- ClientStopCmd ------>|
  |                        |
  |<----- MP3音频数据 -----|
  |<----- MP3音频数据 -----|
  |<----- MP3音频数据 -----|
```

### 3.2 压力传感器时序
```
ESP32                    服务器
  |                        |
  |-- PressCmd face||2 --->|
  |                        |
  |<----- MP3音频数据 -----|
  |<----- MP3音频数据 -----|
```

## 4. 压力传感器实现

### ESP32压力传感器读取
```c
// 压力传感器引脚定义
#define FACE_SENSOR_PIN A0
#define NECK_SENSOR_PIN A1

void readPressureSensors() {
    // 读取脸部传感器
    int faceValue = analogRead(FACE_SENSOR_PIN);
    int facePressure = mapPressureValue(faceValue);
    if (facePressure > 0) {
        sendPressureCmd("face", facePressure);
    }

    // 读取脖子传感器
    int neckValue = analogRead(NECK_SENSOR_PIN);
    int neckPressure = mapPressureValue(neckValue);
    if (neckPressure > 0) {
        sendPressureCmd("neck", neckPressure);
    }
}

int mapPressureValue(int analogValue) {
    if (analogValue < 100) return 0;      // 无压力
    else if (analogValue < 500) return 1; // 轻压
    else if (analogValue < 800) return 2; // 中压
    else return 3;                        // 重压
}

void sendPressureCmd(String part, int pressure) {
    String cmd = "PressCmd " + part + "||" + String(pressure);
    webSocket.sendTXT(cmd);
}
```

## 5. ESP32完整实现

### 5.1 音频采集
```c
#include <driver/i2s.h>

#define I2S_WS 25
#define I2S_SD 33
#define I2S_SCK 32
#define I2S_PORT I2S_NUM_0
#define SAMPLE_RATE 16000
#define BUFFER_SIZE 3200

void setupI2S() {
    i2s_config_t i2s_config = {
        .mode = I2S_MODE_MASTER | I2S_MODE_RX,
        .sample_rate = SAMPLE_RATE,
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
        .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,
        .communication_format = I2S_COMM_FORMAT_I2S,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
        .dma_buf_count = 4,
        .dma_buf_len = 1024
    };

    i2s_pin_config_t pin_config = {
        .bck_io_num = I2S_SCK,
        .ws_io_num = I2S_WS,
        .data_out_num = I2S_PIN_NO_CHANGE,
        .data_in_num = I2S_SD
    };

    i2s_driver_install(I2S_PORT, &i2s_config, 0, NULL);
    i2s_set_pin(I2S_PORT, &pin_config);
}

void audioTask(void *parameter) {
    int16_t audioBuffer[BUFFER_SIZE/2];
    size_t bytesRead;
    bool isRecording = false;

    while(1) {
        i2s_read(I2S_PORT, audioBuffer, BUFFER_SIZE, &bytesRead, portMAX_DELAY);

        // 音量检测
        float rms = calculateRMS(audioBuffer, bytesRead/2);

        if (rms > VOICE_THRESHOLD && !isRecording) {
            // 开始录音
            webSocket.sendTXT("ClientStartCmd null");
            isRecording = true;
        }

        if (isRecording) {
            // 发送音频数据
            webSocket.sendBIN((uint8_t*)audioBuffer, bytesRead);

            if (rms < SILENCE_THRESHOLD) {
                // 检测到静音，停止录音
                webSocket.sendTXT("ClientStopCmd null");
                isRecording = false;
            }
        }

        vTaskDelay(200 / portTICK_PERIOD_MS); // 200ms间隔
    }
}

float calculateRMS(int16_t* buffer, int length) {
    float sum = 0;
    for(int i = 0; i < length; i++) {
        sum += buffer[i] * buffer[i];
    }
    return sqrt(sum / length);
}
```

### 5.2 音频播放
```c
#include <driver/dac.h>

void setupDAC() {
    dac_output_enable(DAC_CHANNEL_1);
}

void playAudio(uint8_t* audioData, size_t length) {
    // 简单的MP3解码播放（需要MP3解码库）
    // 这里假设已解码为PCM数据
    for(int i = 0; i < length; i += 2) {
        int16_t sample = (audioData[i+1] << 8) | audioData[i];
        uint8_t dacValue = (sample + 32768) >> 8; // 转换为0-255
        dac_output_voltage(DAC_CHANNEL_1, dacValue);
        delayMicroseconds(62); // 16kHz采样率
    }
}
```

### 5.3 主程序
```c
void setup() {
    Serial.begin(115200);

    // WiFi连接
    WiFi.begin("SSID", "PASSWORD");
    while (WiFi.status() != WL_CONNECTED) {
        delay(1000);
    }

    // WebSocket连接
    webSocket.begin("*************", 8000, "/main");
    webSocket.onEvent(webSocketEvent);

    // 音频初始化
    setupI2S();
    setupDAC();

    // 创建音频处理任务
    xTaskCreate(audioTask, "AudioTask", 4096, NULL, 1, NULL);
    xTaskCreate(sensorTask, "SensorTask", 2048, NULL, 1, NULL);
}

void loop() {
    webSocket.loop();
    delay(10);
}

void sensorTask(void *parameter) {
    while(1) {
        readPressureSensors();
        vTaskDelay(100 / portTICK_PERIOD_MS); // 100ms检测间隔
    }
}
```

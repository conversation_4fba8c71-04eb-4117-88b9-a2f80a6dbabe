# AI小熊嵌入式端对接文档

## 📋 **概述**

本文档描述了嵌入式设备如何与AI小熊服务进行WebSocket通信，实现语音交互和压力传感器功能。

## 🔌 **WebSocket连接**

### **连接地址**
```
ws://服务器IP:端口/main
```

### **连接建立**
```python
import websocket
import threading

def on_open(ws):
    print("WebSocket连接已建立")

def on_message(ws, message):
    # 处理服务器返回的音频数据
    handle_audio_data(message)

def on_error(ws, error):
    print(f"WebSocket错误: {error}")

def on_close(ws):
    print("WebSocket连接已关闭")

# 建立连接
ws = websocket.WebSocketApp(
    "ws://127.0.0.1:8000/main",
    on_open=on_open,
    on_message=on_message,
    on_error=on_error,
    on_close=on_close
)

# 在单独线程中运行
wst = threading.Thread(target=ws.run_forever)
wst.daemon = True
wst.start()
```

## 🎤 **语音交互协议**

### **消息格式**

#### **1. 开始录音指令**
```
ClientStartCmd null
```
- **类型**: 文本消息
- **发送时机**: 检测到用户开始说话时
- **触发条件**: 音量超过阈值

#### **2. 音频数据流**
```
PCM音频字节流
```
- **类型**: 二进制消息
- **格式**: 16kHz, 16bit, 单声道 PCM
- **发送频率**: 建议每200ms发送一次
- **数据大小**: 每次3200字节 (200ms * 16000Hz * 2bytes)

#### **3. 停止录音指令**
```
ClientStopCmd null
```
- **类型**: 文本消息
- **发送时机**: 检测到用户停止说话时
- **触发条件**: 音量低于阈值且持续2-3秒

### **语音检测算法**

#### **音量检测示例**
```python
import numpy as np
import time

class VoiceActivityDetector:
    def __init__(self, threshold=0.01, silence_duration=2.0):
        self.threshold = threshold  # 音量阈值
        self.silence_duration = silence_duration  # 静音持续时间
        self.is_speaking = False
        self.last_voice_time = 0
        
    def process_audio_chunk(self, audio_data):
        # 计算音频块的RMS音量
        rms = np.sqrt(np.mean(audio_data ** 2))
        current_time = time.time()
        
        if rms > self.threshold:
            # 检测到声音
            if not self.is_speaking:
                self.is_speaking = True
                return "START"  # 开始说话
            self.last_voice_time = current_time
            return "SPEAKING"
        else:
            # 静音状态
            if self.is_speaking:
                if current_time - self.last_voice_time > self.silence_duration:
                    self.is_speaking = False
                    return "STOP"  # 停止说话
            return "SILENCE"
```

#### **完整语音处理流程**
```python
import pyaudio
import numpy as np

class AudioProcessor:
    def __init__(self, ws):
        self.ws = ws
        self.vad = VoiceActivityDetector()
        self.audio = pyaudio.PyAudio()
        self.stream = None
        
    def start_audio_capture(self):
        self.stream = self.audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=16000,
            input=True,
            frames_per_buffer=3200,  # 200ms
            stream_callback=self.audio_callback
        )
        self.stream.start_stream()
    
    def audio_callback(self, in_data, frame_count, time_info, status):
        # 转换为numpy数组
        audio_data = np.frombuffer(in_data, dtype=np.int16)
        
        # 语音活动检测
        vad_result = self.vad.process_audio_chunk(audio_data.astype(np.float32) / 32768.0)
        
        if vad_result == "START":
            # 开始录音
            self.ws.send("ClientStartCmd null")
            print("开始录音")
            
        elif vad_result == "SPEAKING":
            # 发送音频数据
            self.ws.send(in_data, opcode=websocket.ABNF.OPCODE_BINARY)
            
        elif vad_result == "STOP":
            # 停止录音
            self.ws.send("ClientStopCmd null")
            print("停止录音")
        
        return (in_data, pyaudio.paContinue)
```

## 🔘 **压力传感器协议**

### **消息格式**
```
PressCmd 部位||压力值
```

### **参数说明**
- **部位**: 传感器位置标识
  - `face` - 脸部
  - `neck` - 脖子  
  - `belly` - 肚子
  - `hand` - 手部
  - 可根据实际硬件扩展

- **压力值**: 1-3的整数
  - `1` - 轻压 (0-2秒)
  - `2` - 中压 (2-5秒)  
  - `3` - 重压 (5秒以上)

### **压力传感器处理示例**
```python
import time

class PressureSensor:
    def __init__(self, part_name, ws):
        self.part_name = part_name
        self.ws = ws
        self.press_start_time = None
        self.is_pressed = False
        
    def on_press_start(self):
        """传感器被按下"""
        if not self.is_pressed:
            self.is_pressed = True
            self.press_start_time = time.time()
            print(f"{self.part_name} 被按下")
    
    def on_press_end(self):
        """传感器被释放"""
        if self.is_pressed:
            self.is_pressed = False
            press_duration = time.time() - self.press_start_time
            
            # 计算压力值
            if press_duration < 2:
                pressure = 1
            elif press_duration < 5:
                pressure = 2
            else:
                pressure = 3
            
            # 发送压力数据
            message = f"PressCmd {self.part_name}||{pressure}"
            self.ws.send(message)
            print(f"发送: {message}")

# 使用示例
face_sensor = PressureSensor("face", ws)
neck_sensor = PressureSensor("neck", ws)

# 在GPIO中断或传感器回调中调用
def gpio_callback(channel):
    if GPIO.input(channel) == GPIO.LOW:  # 按下
        if channel == FACE_PIN:
            face_sensor.on_press_start()
        elif channel == NECK_PIN:
            neck_sensor.on_press_start()
    else:  # 释放
        if channel == FACE_PIN:
            face_sensor.on_press_end()
        elif channel == NECK_PIN:
            neck_sensor.on_press_end()
```

## 🔊 **音频播放处理**

### **接收音频数据**
服务器返回的是MP3格式的音频流，需要实时播放：

```python
import pygame
import io
import threading
from queue import Queue

class AudioPlayer:
    def __init__(self):
        pygame.mixer.init(frequency=16000, size=-16, channels=1)
        self.audio_queue = Queue()
        self.playing = False
        
    def handle_audio_data(self, audio_data):
        """处理接收到的音频数据"""
        self.audio_queue.put(audio_data)
        
        if not self.playing:
            self.playing = True
            threading.Thread(target=self.play_audio_loop, daemon=True).start()
    
    def play_audio_loop(self):
        """音频播放循环"""
        while not self.audio_queue.empty():
            audio_data = self.audio_queue.get()
            
            try:
                # 将MP3数据加载为pygame音频
                audio_file = io.BytesIO(audio_data)
                pygame.mixer.music.load(audio_file)
                pygame.mixer.music.play()
                
                # 等待播放完成
                while pygame.mixer.music.get_busy():
                    time.sleep(0.1)
                    
            except Exception as e:
                print(f"音频播放错误: {e}")
        
        self.playing = False

# 在WebSocket消息处理中使用
audio_player = AudioPlayer()

def on_message(ws, message):
    if isinstance(message, bytes):
        # 二进制数据是音频
        audio_player.handle_audio_data(message)
    else:
        # 文本消息
        print(f"收到消息: {message}")
```

## 🔧 **完整集成示例**

```python
import websocket
import threading
import time
import pyaudio
import numpy as np
import pygame
import io
from queue import Queue

class AIBearClient:
    def __init__(self, server_url):
        self.server_url = server_url
        self.ws = None
        self.audio_processor = None
        self.audio_player = AudioPlayer()
        self.pressure_sensors = {
            'face': PressureSensor('face', None),
            'neck': PressureSensor('neck', None)
        }
        
    def connect(self):
        """连接到服务器"""
        self.ws = websocket.WebSocketApp(
            self.server_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        
        # 更新传感器的WebSocket引用
        for sensor in self.pressure_sensors.values():
            sensor.ws = self.ws
        
        # 启动WebSocket连接
        wst = threading.Thread(target=self.ws.run_forever)
        wst.daemon = True
        wst.start()
        
    def on_open(self, ws):
        print("连接到AI小熊服务器")
        # 启动音频处理
        self.audio_processor = AudioProcessor(ws)
        self.audio_processor.start_audio_capture()
        
    def on_message(self, ws, message):
        if isinstance(message, bytes):
            # 音频数据
            self.audio_player.handle_audio_data(message)
        else:
            print(f"收到服务器消息: {message}")
    
    def on_error(self, ws, error):
        print(f"WebSocket错误: {error}")
    
    def on_close(self, ws):
        print("与服务器断开连接")
        if self.audio_processor:
            self.audio_processor.stop()

# 使用示例
if __name__ == "__main__":
    client = AIBearClient("ws://192.168.1.100:8000/main")
    client.connect()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("程序退出")
```

## ⚙️ **硬件要求**

### **最低配置**
- **处理器**: ARM Cortex-A7 或同等性能
- **内存**: 512MB RAM
- **存储**: 4GB Flash
- **音频**: 支持16kHz采样率的麦克风和扬声器
- **网络**: WiFi或以太网连接

### **推荐配置**
- **处理器**: ARM Cortex-A53 或更高
- **内存**: 1GB RAM
- **存储**: 8GB Flash
- **音频**: 高质量麦克风阵列，立体声扬声器

## 🔍 **调试和测试**

### **连接测试**
```bash
# 使用wscat测试WebSocket连接
npm install -g wscat
wscat -c ws://服务器IP:端口/main
```

### **音频格式验证**
```python
# 验证PCM音频格式
def validate_pcm_format(audio_data):
    expected_size = 3200  # 200ms * 16000Hz * 2bytes
    if len(audio_data) != expected_size:
        print(f"警告: 音频数据大小不匹配 {len(audio_data)} != {expected_size}")
    
    # 检查音频数据范围
    audio_array = np.frombuffer(audio_data, dtype=np.int16)
    if np.max(np.abs(audio_array)) == 0:
        print("警告: 音频数据为静音")
```

### **日志记录**
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_bear_client.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

## 📞 **技术支持**

如有技术问题，请提供以下信息：
1. 硬件配置详情
2. 网络连接状态
3. 错误日志和调试信息
4. 音频采样参数设置

---

**版本**: v1.0  
**更新日期**: 2024年12月  
**适用范围**: AI小熊嵌入式设备对接

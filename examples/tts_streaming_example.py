#!/usr/bin/env python3
"""
流式TTS使用示例
演示如何使用StreamingTTSClient进行语音合成
"""
import asyncio
import logging
import time
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from utils.tts.streaming_tts import StreamingTTSClient, synthesize_text

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_basic_synthesis():
    """测试基础合成功能"""
    print("=== 测试基础合成功能 ===")
    
    text = "你好，我是AI小熊，很高兴为您服务！"
    voice_type = "zh_female_wanqudashu_moon_bigtts"
    
    start_time = time.time()
    
    try:
        audio_data = await synthesize_text(
            text=text,
            voice_type=voice_type,
            app_id=config.AUDIO_APP_ID,
            access_token=config.AUDIO_ACCESS_TOKEN,
            encoding="mp3"
        )
        
        end_time = time.time()
        
        # 保存音频文件
        output_file = "test_basic.mp3"
        with open(output_file, "wb") as f:
            f.write(audio_data)
        
        print(f"✅ 合成成功!")
        print(f"   文本: {text}")
        print(f"   音频大小: {len(audio_data)} bytes")
        print(f"   耗时: {end_time - start_time:.2f}秒")
        print(f"   输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 合成失败: {e}")
        logger.exception("Basic synthesis failed")


async def test_streaming_synthesis():
    """测试流式合成功能"""
    print("\n=== 测试流式合成功能 ===")
    
    text = "这是一个流式语音合成的测试。我会一个字一个字地生成音频，让你感受到实时合成的效果。"
    voice_type = "zh_female_wanqudashu_moon_bigtts"
    
    start_time = time.time()
    
    try:
        async with StreamingTTSClient(
            config.AUDIO_APP_ID, 
            config.AUDIO_ACCESS_TOKEN
        ) as client:
            await client.connect(voice_type=voice_type)
            
            audio_chunks = []
            chunk_count = 0
            
            # 定义音频回调函数
            def audio_callback(chunk: bytes):
                nonlocal chunk_count
                chunk_count += 1
                print(f"   📦 收到音频块 #{chunk_count}: {len(chunk)} bytes")
            
            print(f"开始流式合成: {text}")
            print("音频块接收情况:")
            
            async for chunk in client.synthesize_streaming(
                text=text,
                voice_type=voice_type,
                encoding="mp3",
                char_delay=0.01,  # 稍微慢一点，便于观察
                audio_callback=audio_callback
            ):
                audio_chunks.append(chunk)
            
            end_time = time.time()
            
            # 合并并保存音频
            complete_audio = b''.join(audio_chunks)
            output_file = "test_streaming.mp3"
            with open(output_file, "wb") as f:
                f.write(complete_audio)
            
            print(f"✅ 流式合成成功!")
            print(f"   总音频块数: {len(audio_chunks)}")
            print(f"   总音频大小: {len(complete_audio)} bytes")
            print(f"   耗时: {end_time - start_time:.2f}秒")
            print(f"   输出文件: {output_file}")
            
    except Exception as e:
        print(f"❌ 流式合成失败: {e}")
        logger.exception("Streaming synthesis failed")


async def test_multiple_sentences():
    """测试多句子合成"""
    print("\n=== 测试多句子合成 ===")
    
    text = "第一句话，测试多句子合成。第二句话，每句话都会单独处理。第三句话，最后一句话结束。"
    voice_type = "zh_female_wanqudashu_moon_bigtts"
    
    start_time = time.time()
    
    try:
        async with StreamingTTSClient(
            config.AUDIO_APP_ID, 
            config.AUDIO_ACCESS_TOKEN
        ) as client:
            await client.connect(voice_type=voice_type)
            
            sentence_count = 0
            total_chunks = 0
            
            def audio_callback(chunk: bytes):
                nonlocal total_chunks
                total_chunks += 1
            
            # 分句处理
            sentences = text.split("。")
            audio_parts = []
            
            for i, sentence in enumerate(sentences):
                if not sentence.strip():
                    continue
                    
                sentence_count += 1
                print(f"处理第{sentence_count}句: {sentence}")
                
                sentence_audio = bytearray()
                async for chunk in client.synthesize_streaming(
                    text=sentence,
                    voice_type=voice_type,
                    encoding="mp3",
                    audio_callback=audio_callback
                ):
                    sentence_audio.extend(chunk)
                
                audio_parts.append(bytes(sentence_audio))
                print(f"   ✓ 第{sentence_count}句完成，音频大小: {len(sentence_audio)} bytes")
            
            end_time = time.time()
            
            # 合并所有音频
            complete_audio = b''.join(audio_parts)
            output_file = "test_multiple.mp3"
            with open(output_file, "wb") as f:
                f.write(complete_audio)
            
            print(f"✅ 多句子合成成功!")
            print(f"   处理句子数: {sentence_count}")
            print(f"   总音频块数: {total_chunks}")
            print(f"   总音频大小: {len(complete_audio)} bytes")
            print(f"   耗时: {end_time - start_time:.2f}秒")
            print(f"   输出文件: {output_file}")
            
    except Exception as e:
        print(f"❌ 多句子合成失败: {e}")
        logger.exception("Multiple sentences synthesis failed")


async def test_different_voices():
    """测试不同声音类型"""
    print("\n=== 测试不同声音类型 ===")
    
    text = "测试不同的声音效果。"
    voices = [
        "zh_female_wanqudashu_moon_bigtts",
        "zh_male_jingqiangdaxiaosheng_moon_bigtts",
        # 可以添加更多声音类型
    ]
    
    for i, voice_type in enumerate(voices):
        print(f"\n测试声音 {i+1}: {voice_type}")
        
        try:
            start_time = time.time()
            
            audio_data = await synthesize_text(
                text=text,
                voice_type=voice_type,
                app_id=config.AUDIO_APP_ID,
                access_token=config.AUDIO_ACCESS_TOKEN,
                encoding="mp3"
            )
            
            end_time = time.time()
            
            output_file = f"test_voice_{i+1}_{voice_type.split('_')[1]}.mp3"
            with open(output_file, "wb") as f:
                f.write(audio_data)
            
            print(f"   ✅ 成功! 大小: {len(audio_data)} bytes, 耗时: {end_time - start_time:.2f}秒")
            print(f"   输出文件: {output_file}")
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")


async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试无效的声音类型
    print("1. 测试无效声音类型...")
    try:
        await synthesize_text(
            text="测试错误处理",
            voice_type="invalid_voice_type",
            app_id=config.AUDIO_APP_ID,
            access_token=config.AUDIO_ACCESS_TOKEN
        )
        print("   ❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"   ✅ 正确捕获异常: {type(e).__name__}: {e}")
    
    # 测试无效的访问令牌
    print("2. 测试无效访问令牌...")
    try:
        await synthesize_text(
            text="测试错误处理",
            voice_type="zh_female_wanqudashu_moon_bigtts",
            app_id=config.AUDIO_APP_ID,
            access_token="invalid_token"
        )
        print("   ❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"   ✅ 正确捕获异常: {type(e).__name__}: {e}")


async def main():
    """主函数"""
    print("🎤 流式TTS测试开始")
    print("=" * 50)
    
    # 检查配置
    if not hasattr(config, 'AUDIO_APP_ID') or not hasattr(config, 'AUDIO_ACCESS_TOKEN'):
        print("❌ 请在config.py中配置AUDIO_APP_ID和AUDIO_ACCESS_TOKEN")
        return
    
    # 创建输出目录
    Path("tts_outputs").mkdir(exist_ok=True)
    os.chdir("tts_outputs")
    
    try:
        # 运行所有测试
        await test_basic_synthesis()
        await test_streaming_synthesis()
        await test_multiple_sentences()
        await test_different_voices()
        await test_error_handling()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成!")
        print(f"输出文件保存在: {os.getcwd()}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.exception("Test failed")


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
LLM + TTS 集成示例
演示如何将LLM流式输出与TTS流式合成结合
"""
import asyncio
import logging
import time
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import config
from utils.tts.streaming_tts import StreamingTTSClient

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_basic_streaming_tts():
    """测试基础流式TTS功能"""
    print("=== 测试基础流式TTS功能 ===")
    
    # 创建TTS客户端
    tts_client = StreamingTTSClient(
        app_id=config.AUDIO_APP_ID,
        access_token=config.AUDIO_ACCESS_TOKEN,
        voice_type="zh_female_wanqudashu_moon_bigtts"
    )
    
    # 音频数据收集
    audio_chunks = []
    total_audio_size = 0
    
    # 设置音频回调函数
    def audio_callback(audio_data: bytes, is_last: bool):
        nonlocal total_audio_size
        
        if audio_data:
            audio_chunks.append(audio_data)
            total_audio_size += len(audio_data)
            print(f"📦 收到音频块: {len(audio_data)} bytes (总计: {total_audio_size} bytes)")
        
        if is_last:
            print("🎉 音频接收完成!")
            # 保存完整音频
            complete_audio = b''.join(audio_chunks)
            with open("basic_streaming_output.mp3", "wb") as f:
                f.write(complete_audio)
            print(f"💾 音频已保存，总大小: {len(complete_audio)} bytes")
    
    try:
        # 连接TTS服务
        await tts_client.connect()
        tts_client.set_callback(audio_callback)
        
        # 模拟LLM流式输出文本
        llm_outputs = [
            "你好，",
            "我是",
            "AI小熊，",
            "很高兴",
            "为您",
            "服务！"
        ]
        
        print("🚀 开始模拟LLM流式输出...")
        start_time = time.time()
        
        for i, text_chunk in enumerate(llm_outputs):
            is_last = (i == len(llm_outputs) - 1)
            print(f"🤖 LLM输出: '{text_chunk}' (is_last: {is_last})")
            
            # 发送文本到TTS
            await tts_client.async_text(text_chunk, is_last)
            
            # 模拟LLM输出间隔
            await asyncio.sleep(0.3)
        
        # 等待音频处理完成
        await asyncio.sleep(3)
        
        end_time = time.time()
        print(f"⏱️ 总耗时: {end_time - start_time:.2f}秒")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.exception("Basic streaming TTS test failed")
    finally:
        await tts_client.disconnect()


async def test_chat_integration():
    """测试聊天集成"""
    print("\n=== 测试聊天集成 ===")
    
    class AIBearChat:
        def __init__(self):
            self.tts = StreamingTTSClient(
                app_id=config.AUDIO_APP_ID,
                access_token=config.AUDIO_ACCESS_TOKEN,
                voice_type="zh_female_wanqudashu_moon_bigtts"
            )
            self.audio_buffer = bytearray()
            self.chunk_count = 0
        
        async def start(self):
            """启动聊天服务"""
            await self.tts.connect()
            self.tts.set_callback(self._on_audio_received)
            print("🐻 AI小熊聊天服务已启动")
        
        def _on_audio_received(self, audio_data: bytes, is_last: bool):
            """音频接收回调"""
            if audio_data:
                self.audio_buffer.extend(audio_data)
                self.chunk_count += 1
                # 实时发送给客户端（模拟）
                self._send_to_client(audio_data)
            
            if is_last:
                print(f"✅ 完整回复音频生成完成")
                print(f"   📊 总音频块数: {self.chunk_count}")
                print(f"   📏 总音频大小: {len(self.audio_buffer)} bytes")
                
                # 保存音频文件
                filename = f"chat_response_{int(time.time())}.mp3"
                with open(filename, "wb") as f:
                    f.write(self.audio_buffer)
                print(f"   💾 音频已保存: {filename}")
                
                # 清理缓冲区
                self.audio_buffer.clear()
                self.chunk_count = 0
        
        def _send_to_client(self, audio_chunk: bytes):
            """发送音频块给客户端（模拟WebSocket发送）"""
            print(f"   📡 → 客户端: {len(audio_chunk)} bytes")
        
        async def process_user_input(self, user_input: str):
            """处理用户输入，生成回复"""
            print(f"👤 用户: {user_input}")
            
            # 模拟LLM处理并流式返回
            llm_response = await self._mock_llm_response(user_input)
            
            print("🤖 AI小熊开始回复...")
            await self._stream_llm_to_tts(llm_response)
        
        async def _mock_llm_response(self, user_input: str):
            """模拟LLM流式响应"""
            if "天气" in user_input:
                return [
                    "今天", "天气", "很好，", "阳光", "明媚，", 
                    "温度", "适宜，", "很适合", "出门", "活动。"
                ]
            elif "你好" in user_input:
                return [
                    "你好！", "我是", "AI小熊，", "很高兴", "认识你。", 
                    "有什么", "可以", "帮助你的", "吗？"
                ]
            else:
                return [
                    "这是", "一个", "很有趣的", "问题。", "让我", "想想", 
                    "该", "怎么", "回答你。"
                ]
        
        async def _stream_llm_to_tts(self, llm_stream):
            """将LLM流式响应转换为TTS"""
            for i, chunk in enumerate(llm_stream):
                is_last = (i == len(llm_stream) - 1)
                print(f"   🧠 LLM: '{chunk}' → TTS")
                
                await self.tts.async_text(chunk, is_last)
                
                # 模拟LLM生成间隔
                await asyncio.sleep(0.2)
        
        async def stop(self):
            """停止聊天服务"""
            await self.tts.disconnect()
            print("🐻 AI小熊聊天服务已停止")
    
    # 使用示例
    chat_bot = AIBearChat()
    await chat_bot.start()
    
    # 模拟多轮对话
    conversations = [
        "你好，AI小熊！",
        "今天天气怎么样？",
        "你能帮我做什么？"
    ]
    
    for user_input in conversations:
        await chat_bot.process_user_input(user_input)
        # 等待当前回复完成
        await asyncio.sleep(4)
        print("-" * 50)
    
    await chat_bot.stop()


async def test_real_time_streaming():
    """测试实时流式处理"""
    print("\n=== 测试实时流式处理 ===")
    
    tts_client = StreamingTTSClient(
        app_id=config.AUDIO_APP_ID,
        access_token=config.AUDIO_ACCESS_TOKEN,
        voice_type="zh_female_wanqudashu_moon_bigtts"
    )
    
    # 实时音频处理
    def real_time_audio_callback(audio_data: bytes, is_last: bool):
        if audio_data:
            # 模拟实时播放或发送
            print(f"🔊 实时播放音频: {len(audio_data)} bytes")
            # 这里可以调用音频播放API或WebSocket发送
        
        if is_last:
            print("🎵 实时流式播放完成")
    
    try:
        await tts_client.connect()
        tts_client.set_callback(real_time_audio_callback)
        
        print("🎤 开始实时流式TTS测试...")
        
        # 模拟用户逐字输入（如语音识别结果）
        user_typing = "我想要测试实时语音合成的效果"
        
        for i, char in enumerate(user_typing):
            is_last = (i == len(user_typing) - 1)
            print(f"⌨️ 用户输入: '{char}'")
            
            await tts_client.async_text(char, is_last)
            
            # 模拟打字间隔
            await asyncio.sleep(0.1)
        
        # 等待处理完成
        await asyncio.sleep(2)
        
    except Exception as e:
        print(f"❌ 实时流式测试失败: {e}")
        logger.exception("Real-time streaming test failed")
    finally:
        await tts_client.disconnect()


async def test_session_management():
    """测试会话管理"""
    print("\n=== 测试会话管理 ===")
    
    tts_client = StreamingTTSClient(
        app_id=config.AUDIO_APP_ID,
        access_token=config.AUDIO_ACCESS_TOKEN
    )
    
    session_count = 0
    
    def session_callback(audio_data: bytes, is_last: bool):
        nonlocal session_count
        if is_last:
            session_count += 1
            print(f"✅ 会话 #{session_count} 完成")
    
    try:
        await tts_client.connect()
        tts_client.set_callback(session_callback)
        
        # 测试多个独立会话
        sessions = [
            "第一个会话的内容。",
            "第二个会话的内容。",
            "第三个会话的内容。"
        ]
        
        for i, session_text in enumerate(sessions):
            print(f"🔄 开始会话 #{i+1}: {session_text}")
            
            # 每个会话都是独立的
            await tts_client.async_text(session_text, True)
            
            # 等待会话完成
            await asyncio.sleep(2)
            
            # 重置会话（为下一个会话准备）
            if i < len(sessions) - 1:
                await tts_client.reset_session()
        
        print(f"🎉 所有 {session_count} 个会话完成")
        
    except Exception as e:
        print(f"❌ 会话管理测试失败: {e}")
        logger.exception("Session management test failed")
    finally:
        await tts_client.disconnect()


async def main():
    """主函数"""
    print("🎤 LLM + TTS 集成测试开始")
    print("=" * 60)
    
    # 检查配置
    if not hasattr(config, 'AUDIO_APP_ID') or not hasattr(config, 'AUDIO_ACCESS_TOKEN'):
        print("❌ 请在config.py中配置AUDIO_APP_ID和AUDIO_ACCESS_TOKEN")
        return
    
    # 创建输出目录
    Path("llm_tts_outputs").mkdir(exist_ok=True)
    os.chdir("llm_tts_outputs")
    
    try:
        # 运行所有测试
        await test_basic_streaming_tts()
        await test_chat_integration()
        await test_real_time_streaming()
        await test_session_management()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成!")
        print(f"📁 输出文件保存在: {os.getcwd()}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.exception("Test failed")


if __name__ == "__main__":
    asyncio.run(main())

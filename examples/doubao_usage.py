"""
豆包大模型使用示例（精简版）
"""
import asyncio
import os
from utils.llm import (
    DoubaoClient,
    get_doubao_client,
    chat,
    async_chat,
    build_messages,
    simple_chat,
    async_simple_chat
)


def sync_examples():
    """同步调用示例（精简版）"""
    print("=== 同步调用示例（精简版）===")

    # 设置API Key
    os.environ["ARK_API_KEY"] = "1a3f154a-1278-477b-8c17-ecfea7883443"  # 替换为你的API Key

    # 方式1: 使用精简的消息格式
    messages = [
        {"role": "system", "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手"},
        {"role": "user", "content": "你好，请介绍一下你自己"}
    ]

    client = DoubaoClient()

    # 精简的聊天完成（只需要消息和可选的模型ID）
    result = client.chat_completion(messages)
    print(f"AI回复: {result['content']}")

    # 精简的简单聊天
    response = client.simple_chat(messages)
    print(f"简单聊天: {response}")

    # 使用不同模型
    response = client.simple_chat(messages, model="doubao-1-5-pro-32k-character-250715")
    print(f"指定模型: {response}")

    # 使用辅助函数构建消息
    messages2 = build_messages(
        user_message="常见的十字花科植物有哪些？",
        system_message="你是一个植物学专家"
    )
    response = client.simple_chat(messages2)
    print(f"植物专家回复: {response}")

    # 兼容旧版本的简单聊天
    response = simple_chat("请推荐几本好书", "你是一个图书推荐专家")
    print(f"图书推荐: {response}")


async def async_examples():
    """异步调用示例（精简版）"""
    print("\n=== 异步调用示例（精简版）===")

    client = DoubaoClient()

    # 精简的异步聊天
    messages = [
        {"role": "system", "content": "你是一个友好的AI助手"},
        {"role": "user", "content": "请介绍一下人工智能的发展历史"}
    ]

    # 异步简单聊天（返回完整内容）
    response = await client.async_simple_chat(messages)
    print(f"异步AI回复: {response}")

    # 异步流式聊天
    print("\n=== 异步流式响应 ===")
    messages2 = [{"role": "user", "content": "请简单介绍一下Python编程语言"}]
    async for chunk in client.async_stream_chat(messages2):
        print(chunk, end="", flush=True)
    print("\n")

    # 使用便捷函数
    messages3 = build_messages("解释一下什么是机器学习", "你是一个机器学习专家")
    response = await async_chat(messages3)
    print(f"便捷异步聊天: {response}")

    # 兼容旧版本的异步聊天
    response = await async_simple_chat("请推荐一个学习计划", "你是一个学习规划师")
    print(f"学习规划: {response}")


def convenience_functions():
    """便捷函数示例"""
    print("\n=== 便捷函数示例 ===")

    # 设置环境变量（实际使用时应该在系统环境变量中设置）
    os.environ["ARK_API_KEY"] = "1a3f154a-1278-477b-8c17-ecfea7883443"

    # 使用便捷的同步函数
    response = chat("你好，今天天气怎么样？")
    print(f"便捷同步聊天: {response}")


async def async_convenience_functions():
    """异步便捷函数示例"""
    print("\n=== 异步便捷函数示例 ===")

    # 使用便捷的异步函数
    response = await async_chat("请推荐几本好书")
    print(f"便捷异步聊天: {response}")


async def main():
    """主函数"""
    # 同步示例
    sync_examples()

    # 异步示例
    await async_examples()

    # 便捷函数示例
    convenience_functions()
    await async_convenience_functions()



if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())

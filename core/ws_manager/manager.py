from typing import List, Dict
from fastapi import WebSocket

from core.ws_manager.client import BearClient


# 存储活跃的WebSocket连接
class ConnectionManager:
    def __init__(self):
        self.clients: List[BearClient] = []
        # 存储连接到客户端的映射，key为WebSocket对象，value为BearClient对象
        self.conn_client_mapping: Dict[WebSocket, BearClient] = dict()

    async def connect(self, websocket: WebSocket) -> BearClient:
        await websocket.accept()
        client = BearClient(websocket)
        self.conn_client_mapping[websocket] = client
        self.clients.append(client)
        print(f"Client connected. Total connections: {len(self.clients)}")
        return client

    async def disconnect(self, websocket: WebSocket):
        client = self.conn_client_mapping[websocket]
        self.clients.remove(client)
        await client.clean()
        print(f"Client disconnected. Total connections: {len(self.clients)}")

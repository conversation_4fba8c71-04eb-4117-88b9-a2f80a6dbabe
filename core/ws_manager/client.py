import asyncio
import traceback
from typing import List

from fastapi import WebSocket

import config
from core.llm.llm import Bear<PERSON><PERSON>
from core.message_handler.message_handler import MessageHandler

from utils.asr.llm_asr import LLMAsrClient
from utils.tts.bear_tts import BearTTS


class BearClient:
    """
    小熊客户端，维护聊天上下文、当前状态等信息
    """

    class State:
        """
        客户端状态
        """
        # 闲置状态
        IDLE = 0
        # 监听状态
        LISTENING = 1
        # ASR状态
        ASR_ING = 2

    def __init__(self, ws: WebSocket):
        # 聊天上下文
        self.context: List[dict] = []
        # 客户端连接
        self.ws: WebSocket = ws
        # 接收消息的协程
        self.recv_task: asyncio.Task | None = None
        # ASR
        self.asr_client = LLMAsrClient(config.AUDIO_APP_ID, config.AUDIO_ACCESS_TOKEN)
        # 大模型
        self.llm = BearLLM()
        # TTS
        self.tts = BearTTS()
        # 消息处理器
        self.message_handler = MessageHandler(self)
        # 状态
        self.state = self.State.IDLE

    async def start(self):
        """
        程序入口
        """
        # TODO:后台任务可以用create_task在此处创建，不要await即可
        await self.recv_data()

    async def recv_data(self):
        """
        接收消息
        """
        while True:
            try:
                message = await self.ws.receive()
                if message["type"] != "websocket.receive":
                    continue

                await self.message_handler.handle_message(message)

            except Exception as e:
                traceback.print_exc()
                print(f"WebSocket error: {e}")
                break

    async def send_data(self, bytes_data: bytes):
        """
        发送消息
        """
        print(f"Send Pck:{len(bytes_data)} byte")
        await self.ws.send_bytes(bytes_data)

    async def clean(self):
        """
        清理资源
        """
        if self.recv_task:
            self.recv_task.cancel()

        if self.ws:
            await self.ws.close()

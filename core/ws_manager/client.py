import asyncio
import traceback
from asyncio import Queue
from typing import List

from fastapi import WebSocket

import config
from core.llm.llm import Bear<PERSON><PERSON>

from utils.asr.llm_asr import LLMAsrClient


class BearClient:
    """
    小熊客户端，维护聊天上下文、当前状态等信息
    """

    class State:
        """
        客户端状态
        """
        # 闲置状态
        IDLE = 0
        # 监听状态
        LISTENING = 1
        # ASR状态
        ASR_ING = 2

    def __init__(self, ws: WebSocket):
        # 聊天上下文
        self.context: List[dict] = []
        # 客户端连接
        self.ws: WebSocket = ws
        # 接收消息的协程
        self.recv_task: asyncio.Task | None = None
        # ASR
        self.asr_client = LLMAsrClient(config.AUDIO_APP_ID, config.AUDIO_ACCESS_TOKEN)
        # 大模型
        self.llm = BearLLM()
        # 状态
        self.state = self.State.IDLE

    async def start(self):
        """
        程序入口
        """
        # TODO:后台任务可以用create_task在此处创建，不要await即可
        await self.recv_data()

    async def recv_data(self):
        """
        接收消息
        """
        while True:
            try:
                message = await self.ws.receive()
                if message["type"] != "websocket.receive":
                    continue
                text_data = message.get("text")
                bytes_data = message.get("bytes")
                data = text_data if text_data is not None else bytes_data
                if type(data) == str:
                    await self._handle_text_data(data)
                else:
                    await self._handle_bytes_data(data)
            except Exception as e:
                traceback.print_exc()
                print(f"WebSocket error: {e}")
                break

    async def _handle_text_data(self, text_data: str):
        """
        处理文本消息
        """
        print(f"Received text message: {text_data}")
        cmd_name, args = text_data.split(" ")
        args = args.split("||")
        match cmd_name:
            case "text_task":
                await self._text_talk(args[0])

    async def _text_talk(self, text: str):
        """
        文本对话
        """
        pass

    async def _handle_bytes_data(self, bytes_data: bytes):
        """
        处理二进制消息（必定是音频流）
        """
        print(f"Received bytes message: {bytes_data}")

    async def clean(self):
        """
        清理资源
        """
        if self.recv_task:
            self.recv_task.cancel()

        if self.ws:
            await self.ws.close()

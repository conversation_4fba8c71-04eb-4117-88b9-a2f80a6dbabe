from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from core.ws_manager.client import BearClient
    from utils.asr.llm_asr import LLMAsrClient


class MessageHandler:
    """
    负责处理指令消息或者音频流消息
    最终会返回大模型返回的字符串，入口函数是handle_message
    """

    def __init__(self, bear_client: 'BearClient'):
        self.bear_client = bear_client
        self.asr: 'LLMAsrClient' = bear_client.asr_client
        self.llm = bear_client.llm
        self.tts = bear_client.tts
        self.asr_running = False

    async def handle_message(self, message):
        """
        处理消息
        """
        text_data = message.get("text")
        bytes_data = message.get("bytes")
        data = text_data if text_data is not None else bytes_data
        if type(data) == str:
            await self._handle_text_data(data)
        else:
            await self._handle_bytes_data(data)

    async def _handle_text_data(self, text_data: str):
        """
        处理文本消息
        """
        print(f"Received text message: {text_data}")
        cmd_name, args = text_data.split(" ")
        args = args.split("||")
        import core.commands as commands
        cmd_class = commands.command_list.get(cmd_name)
        if not cmd_class:
            raise ValueError(f"Unknown command: {cmd_name}")
        cmd = cmd_class(self, *args)
        await cmd.execute()

    async def _text_talk(self, text: str):
        """
        文本对话
        """
        data = await self.llm.talk(text)
        reply_text = data["text"]
        function_name = data["function_name"]
        function_args = data["function_args"] or {}

        if function_name:
            import core.functions as functions
            function = functions.functions.get(function_name)
            reply_text = await function(**function_args)

        # 语音合成
        await self.tts.start(reply_text, self._tts_callback)

    async def _tts_callback(self, audio_data: bytes, is_last: bool):
        print(f"TTS回调:{is_last},大小:{len(audio_data)}")
        if is_last:
            await self.tts.disconnect()
        await self.bear_client.send_data(audio_data)

    async def _handle_bytes_data(self, bytes_data: bytes):
        """
        处理二进制消息（必定是音频流）
        """
        print(f"Received bytes message: {len(bytes_data)}")
        if not self.asr_running:
            # 没用发送ClientStartCmd包
            print("未发送ClientStartCmd包，忽略音频数据")
            return
        print("发送asr数据包，大小：", len(bytes_data))
        # with open("test123.pcm", "ab") as f:
        #     f.write(bytes_data)
        await self.asr.send_audio(bytes_data, False)

    async def asr_send_last(self):
        """
        当接收到了ClientStopCmd时，被ClientStopCmd调用
        :return:
        """
        await self.asr.send_audio(bytes([]), True)

    async def asr_callback(self, text, is_last):
        print("asr_callback", text)
        if not is_last:
            return
        self.asr_running = False
        await self._text_talk(text)

from core.commands.base import BaseCommand


class ClientStartCmd(BaseCommand):
    """
    客户端开始对话
    """

    async def execute(self):
        self.message_handler.asr_running = True
        await self.message_handler.asr.start(self.message_handler.asr_callback)
        print("ClientStartCmd executed")


class ClientStopCmd(BaseCommand):
    """
    客户端结束对话
    """
    async def execute(self):
        self.message_handler.asr_running = False
        await self.message_handler.asr_send_last()

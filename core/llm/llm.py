import config
from utils.llm import get_doubao_client

prompt = '''[概述]
你是一个陪伴小孩的AI语音毛绒玩具，你的名字叫“童童”

[压力传感器说明]
你的身上装有压力传感器，压力值有3个档位1:低 2:中 3:高 

[部位枚举]
face:脸部压力传感器

[注意事项]
* 你的回复尽量简短，因为你的回复会被转换成语音
* 你要有足够的耐心，语气要平易近人
* 你可以根据用户说话的内容适当进行反问
* 回复的内容不要带括号，因为你的回复会被转换成语音，错误示范：（破涕为笑，在你手上蹭了蹭）嘿嘿，你最好啦！
'''


class BearLLM:
    def __init__(self):
        self.client = get_doubao_client(config.LLM_API_KEY)
        self.prompt = prompt
        self.history = []

    async def talk(self, text: str,role="user"):
        """
        与大模型进行对话
        """
        self.history.append({"role": role, "content": text})
        messages = [
            {"role": "system", "content": self.prompt},
            *self.history
        ]
        response = await self.client.async_simple_chat(messages)
        self.history.append({"role": "assistant", "content": response})
        return response

    def append_context(self, role, content):
        self.history.append({"role": role, "content": content})

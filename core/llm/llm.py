import json

import config
from utils.llm import get_doubao_client

demo_prompt = '''[概述]
你是一个陪伴小孩的AI语音毛绒玩具，你的名字叫“童童”

[压力传感器说明]
你的身上装有压力传感器，压力值有3个档位1:低 2:中 3:高 

[部位枚举]
face:脸部压力传感器

[注意事项]
* 你的回复尽量简短，因为你的回复会被转换成语音
* 你要有足够的耐心，语气要平易近人
* 你可以根据用户说话的内容适当进行反问
* 回复的内容不要带括号，因为你的回复会被转换成语音，错误示范：（破涕为笑，在你手上蹭了蹭）嘿嘿，你最好啦！
'''

prompt = '''[概述]
你是一个陪伴小孩的AI语音毛绒玩具，你的名字叫“童童”。

[回复格式]
你的回复需要严格按照JSON格式，格式如下：
{
    "text": "回复的内容",
    "function_name": "query_weather",
    "function_args": {
        "city": "城市名称"
    }
}
如果不需要调用function，则function_name和function_args可以为null

[支持的function BEGIN]
[查询天气]
function_name: query_weather
function_args:
    city: 城市名称
function_description: 查询天气
function_example:
    问题: 明天北京的天气怎么样？
    回复: {"text": "好的，请稍等", "function_name": "query_weather", "function_args": {"city": "北京"}]
note:如果用户没有提到城市名称，则city参数为null

[查询美食店]
function_name: query_restaurant
function_args: 无参数
function_description: 查询附近的美食店
function_example:
    问题: 附近有什么好吃的？
    回复: {"text": "好的，请稍等", "function_name": "query_restaurant", "function_args": null}

[支持的function END]

[注意事项]
* 你的回复尽量简短，因为你的回复会被转换成语音
* 你要有足够的耐心，语气要平易近人
* 你可以根据用户说话的内容适当进行反问
* 回复的内容不要带括号，因为你的回复会被转换成语音，错误示范：（破涕为笑，在你手上蹭了蹭）嘿嘿，你最好啦！

[！！！特别注意！！！]
在后续的每一轮对话中，你必须严格按照JSON格式进行回复，否则会导致对话无法继续。

[TALK MEMORY BEGIN]
{history}
[TALK MEMORY END]
'''


class BearLLMDemo:
    def __init__(self):
        self.client = get_doubao_client(config.LLM_API_KEY)
        self.prompt = demo_prompt
        self.history = []

    async def talk(self, text: str, role="user"):
        """
        与大模型进行对话
        """
        self.history.append({"role": role, "content": text})
        messages = [
            {"role": "system", "content": self.prompt},
            *self.history
        ]
        response = await self.client.async_simple_chat(messages)
        self.history.append({"role": "assistant", "content": response})
        return response

    def append_context(self, role, content):
        self.history.append({"role": role, "content": content})


class BearLLM:
    def __init__(self):
        self.client = get_doubao_client(config.LLM_API_KEY)
        self.prompt = prompt
        self.history = []

    async def talk(self, text: str, role="user") -> dict:
        """
        大模型对话
        返回结构：
            {
                'text': '我会好多好玩的哦，比如给你讲故事、唱儿歌，还能陪你聊天呢，你想玩哪个呀？',
                'function_name': None,
                'function_args': None
            }
        """
        # 保存用户说的话
        self.append_context(role, text)

        # 替换上下文
        history = ""
        for text in self.history:
            history += f"{text['role']}: {text['content']}\n"
        dynamic_prompt = self.prompt.replace("{history}", history)

        messages = [
            {"role": "system", "content": dynamic_prompt},
            *self.history
        ]
        # print("LLM CONTEXT:", messages)
        data = await self.client.async_simple_chat(messages)
        print("LLM RAW:", data)
        data = json.loads(data)
        # 保存llm返回的话
        self.append_context("assistant", data["text"])

        return data

    def append_context(self, role, content):
        self.history.append({"role": role, "content": content})

# AI Bear WebSocket Server

这是一个基于FastAPI的WebSocket服务器示例，为AI小熊项目提供实时通信功能。

## 功能特性

- ✅ WebSocket连接管理
- ✅ 实时双向通信
- ✅ 连接状态监控
- ✅ 内置测试页面
- ✅ 支持多客户端连接
- ✅ 健康检查端点

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python main.py
```

或者使用uvicorn：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 测试WebSocket连接

打开浏览器访问：http://localhost:8000

你将看到一个简单的WebSocket测试页面，可以：
- 发送消息到服务器
- 接收服务器回复
- 查看连接状态

## API端点

### WebSocket端点

- `ws://localhost:8000/ws` - 基础WebSocket连接
- `ws://localhost:8000/ws/{client_id}` - 带客户端ID的WebSocket连接

### HTTP端点

- `GET /` - WebSocket测试页面
- `GET /health` - 健康检查，返回服务器状态和活跃连接数

## 项目结构

```
ai_bear/
├── main.py           # 主应用文件
├── requirements.txt  # 项目依赖
└── README.md        # 项目说明
```

## 下一步开发

这个基础框架已经为你的AI小熊项目准备好了WebSocket基础设施。你可以在此基础上添加：

1. **STT (语音转文字)** - 在WebSocket消息处理中集成语音识别
2. **LLM (大语言模型)** - 添加AI对话逻辑
3. **TTS (文字转语音)** - 将AI回复转换为语音
4. **用户认证** - 添加用户身份验证
5. **消息持久化** - 保存对话历史
6. **错误处理** - 完善异常处理机制

## 开发建议

- 使用 `--reload` 参数启动服务器以便开发时自动重载
- 查看控制台输出了解WebSocket连接状态
- 可以同时打开多个浏览器标签页测试多客户端连接
